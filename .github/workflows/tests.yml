# This is the main CI workflow that runs the test suite on all pushes to main and all pull requests.
permissions:
  contents: read
on:
  push:
    branches: [main]
    paths:
      - crates/**
      - bin/**
      - Cargo.toml
      - Cargo.lock
      - Dockerfile
  pull_request:
    paths:
      - crates/**
      - bin/**
      - Cargo.toml
      - Cargo.lock
      - Dockerfile
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true
name: unit-tests
jobs:
  required:
    runs-on:
      group: large-runners
    name: ubuntu / ${{ matrix.toolchain }}
    strategy:
      matrix:
        # run on stable and beta to ensure that tests won't break on the next version of the rust
        # toolchain
        toolchain: [stable]
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
      - name: Cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            target/
          key: ${{ runner.os }}-cargo-${{ hashFiles('Cargo.lock') }}
      - name: Install ${{ matrix.toolchain }}
        uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: ${{ matrix.toolchain }}
      - name: cargo test
        run: cargo test --workspace --all-features --all-targets
