exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE orders (
    o_orderkey INTEGER,
    o_clerk VA<PERSON>HA<PERSON>,
    o_totalprice NUMBER(12, 2),
    o_orderstatus CHAR(1));

exclude-from-coverage
statement ok
INSERT INTO orders (o_orderkey, o_orderstatus, o_clerk, o_totalprice)
  VALUES
    ( 32123, 'O', 'Clerk#000000321',     321.23),
    ( 41445, 'F', 'Clerk#000000386', 1041445.00),
    ( 55937, 'O', 'Clerk#000000114', 1055937.00),
    ( 67781, 'F', 'Clerk#000000521', 1067781.00),
    ( 80550, 'O', 'Clerk#000000411', 1080550.00),
    ( 95808, 'F', 'Clerk#000000136', 1095808.00),
    (101700, 'O', 'Clerk#000000220', 1101700.00),
    (103136, 'F', 'Clerk#000000508', 1103136.00);

query TT
SELECT o_orderstatus,
   LISTAGG(o_clerk, ', ')
     WITHIN GROUP (ORDER BY o_totalprice DESC)
  FROM orders
  WHERE o_totalprice > 520000
  GROUP BY o_orderstatus
----
O	Clerk#000000220, Clerk#000000411, Clerk#000000114
F	Clerk#000000508, Clerk#000000136, Clerk#000000521, Clerk#000000386

query T
SELECT LISTAGG(DISTINCT o_orderstatus, '|')
  FROM orders
  WHERE o_totalprice > 520000
----
<REGEX>:^(?=.*\bF\b)(?=.*\bO\b)[FO|]{3}$

exclude-from-coverage
statement ok
USE SCHEMA COMPATIBILITY_TESTS.PUBLIC;

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE collation_demo (
  spanish_phrase VARCHAR COLLATE 'es');

exclude-from-coverage
statement ok
INSERT INTO collation_demo (spanish_phrase) VALUES
  ('piña colada'),
  ('Pinatubo (Mount)'),
  ('pint'),
  ('Pinta');

query T
SELECT LISTAGG(spanish_phrase, '|')
    WITHIN GROUP (ORDER BY COLLATE(spanish_phrase, 'es')) AS es_collation
  FROM collation_demo
----
Pinatubo (Mount)|pint|Pinta|piña colada

query T
SELECT LISTAGG(spanish_phrase, '|')
    WITHIN GROUP (ORDER BY COLLATE(spanish_phrase, 'utf8')) AS utf8_collation
  FROM collation_demo
----
Pinatubo (Mount)|Pinta|pint|piña colada

