exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE basic_example (i_col INTEGER, j_col INTEGER);

exclude-from-coverage
statement ok
INSERT INTO basic_example VALUES
    (11,101), (11,102), (11,NULL), (12,101), (NULL,101), (NULL,102);

query T
SELECT COUNT_IF(TRUE) FROM basic_example
----
6

query T
SELECT COUNT_IF(j_col > i_col) FROM basic_example
----
3

query T
SELECT COUNT_IF(i_col IS NOT NULL AND j_col IS NOT NULL) FROM basic_example
----
3

