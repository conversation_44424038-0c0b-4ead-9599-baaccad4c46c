exclude-from-coverage
statement ok
create or replace table aggr(k int, v decimal(10,2));

exclude-from-coverage
statement ok
insert into aggr (k, v) values
    (0,  0),
    (0, 10),
    (0, 20),
    (0, 30),
    (0, 40),
    (1, 10),
    (1, 20),
    (2, 10),
    (2, 20),
    (2, 25),
    (2, 30),
    (3, 60),
    (4, NULL);

query TT
select k, percentile_cont(0.25) within group (order by v) 
  from aggr 
  group by k
  order by k
----
0	10.00000
1	12.50000
2	17.50000
3	60.00000
4	NULL

