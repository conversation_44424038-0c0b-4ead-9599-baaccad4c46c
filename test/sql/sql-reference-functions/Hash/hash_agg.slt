exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE orders (
  order_id INT,
  customer_id INT,
  order_date DATE,
  order_status STRING
);

exclude-from-coverage
statement ok
INSERT INTO orders (order_id, customer_id, order_date, order_status) VALUES
  (1, 101, '2023-01-15', 'P'),
  (2, 102, '2023-01-16', 'F'),
  (3, 103, '2023-01-17', 'P'),
  (4, 104, '2023-01-18', 'F'),
  (5, 105, '2023-01-19', 'P');

query T
SELECT HASH_AGG(*) FROM orders;
----
7300095393248939463