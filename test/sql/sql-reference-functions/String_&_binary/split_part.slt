query TT
SELECT column1 part_number_value, column2 portion
  FROM VALUES
    (0, SPLIT_PART('11.22.33', '.',  0)),
    (1, SPLIT_PART('11.22.33', '.',  1)),
    (2, SPLIT_PART('11.22.33', '.',  2)),
    (3, <PERSON>LIT_PART('11.22.33', '.',  3)),
    (4, SPLIT_PART('11.22.33', '.',  4)),
    (-1, SPLIT_PART('11.22.33', '.',  -1)),
    (-2, SPLIT_PART('11.22.33', '.',  -2)),
    (-3, SPLIT_PART('11.22.33', '.',  -3)),
    (-4, SPLIT_PART('11.22.33', '.',  -4))
----
0	11
1	11
2	22
3	33
4	''
-1	33
-2	22
-3	11
-4	''

query TT
SELECT SPLIT_PART('127.0.0.1', '.', 1) AS first_part,
       SPLIT_PART('127.0.0.1', '.', -1) AS last_part
----
127	1

query TT
SELECT SPLIT_PART('|a|b|c|', '|', 1) AS first_part,
       SPLIT_PART('|a|b|c|', '|', 2) AS last_part
----
''	a

query T
SELECT SPLIT_PART('aaa--bbb-BBB--ccc', '--', 2) AS multi_character_separator
----
bbb-BBB

query TT
SELECT column1 part_number_value, column2 portion
  FROM VALUES
    (1, split_part('<EMAIL>', '@',  1)),
    (-1, split_part('<EMAIL>', '@', -1)),
    (2, split_part('<EMAIL>', '@',  2)),
    (-2, split_part('<EMAIL>', '@', -2))
----
1	user
-1	snowflake.com
2	snowflake.com
-2	user

