exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE padding_example (v VARCHAR, b BINARY);

exclude-from-coverage
statement ok
INSERT INTO padding_example (v, b)
  SELECT
    'Hi',
    HEX_ENCODE('Hi');

exclude-from-coverage
statement ok
INSERT INTO padding_example (v, b)
  SELECT
    '-123.00',
    HEX_ENCODE('-123.00');

exclude-from-coverage
statement ok
INSERT INTO padding_example (v, b)
  SELECT
    'Twelve Dollars',
    TO_BINARY(HEX_ENCODE('Twelve Dollars'), 'HEX');

query TTT
SELECT v,
       RPAD(v, 10, '_') AS pad_with_underscore,
       RPAD(v, 10, '$') AS pad_with_dollar_sign
  FROM padding_example
  ORDER BY v
----
-123.00	-123.00___	-123.00$$$
Hi	Hi________	Hi$$$$$$$$
Twelve Dollars	Twelve Dol	Twelve Dol

query TTT
SELECT b,
       RPAD(b, 10, TO_BINARY(HEX_ENCODE('_'))) AS pad_with_underscore,
       RPAD(b, 10, TO_BINARY(HEX_ENCODE('$'))) AS pad_with_dollar_sign
  FROM padding_example
  ORDER BY b
----
x'2d3132332e3030'	x'2d3132332e30305f5f5f'	x'2d3132332e3030242424'
x'4869'	x'48695f5f5f5f5f5f5f5f'	x'48692424242424242424'
x'5477656c766520446f6c6c617273'	x'5477656c766520446f6c'	x'5477656c766520446f6c'

query T
SELECT RPAD('123.50', 19, '*_')
----
123.50*_*_*_*_*_*_*

