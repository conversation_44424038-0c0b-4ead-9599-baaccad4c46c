query T
SELECT DAYNAME(TO_DATE('2024-04-01')) AS DAY
----
Mon

query T
SELECT DAYNAME(TO_TIMESTAMP_NTZ('2024-04-02 10:00')) AS DAY
----
<PERSON><PERSON>

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE dates (d DATE);

exclude-from-coverage
statement ok
INSERT INTO dates (d) VALUES 
  ('2024-01-01'::DATE),
  ('2024-01-02'::DATE),
  ('2024-01-03'::DATE),
  ('2024-01-04'::DATE),
  ('2024-01-05'::DATE),
  ('2024-01-06'::DATE),
  ('2024-01-07'::DATE),
  ('2024-01-08'::DATE);

query TT
SELECT d, DAYNAME(d) 
  FROM dates
  ORDER BY d
----
'2024-01-01'	Mon
'2024-01-02'	Tue
'2024-01-03'	Wed
'2024-01-04'	Thu
'2024-01-05'	Fri
'2024-01-06'	Sat
'2024-01-07'	Sun
'2024-01-08'	Mon

