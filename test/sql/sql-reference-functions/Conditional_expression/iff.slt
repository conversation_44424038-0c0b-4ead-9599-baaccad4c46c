query T
SELECT IFF(TRUE, 'true', 'false')
----
true

query T
SELECT IFF(FALSE, 'true', 'false')
----
false

query T
SELECT IFF(NULL, 'true', 'false')
----
false

query T
SELECT IFF(TRUE, NULL, 'false')
----
NULL

query TT
SELECT value, IFF(value::INT = value, 'integer', 'non-integer')
  FROM ( SELECT column1 AS value
           FROM VALUES(1.0), (1.1), (-3.1415), (-5.000), (NULL) )
  ORDER BY value DESC
----
NULL	non-integer
1.1000	non-integer
1.0000	integer
-3.1415	non-integer
-5.0000	integer

query TT
SELECT value, IFF(value > 50, 'High', 'Low')
FROM ( SELECT column1 AS value
         FROM VALUES(22), (63), (5), (99), (NULL) )
----
22	Low
63	High
5	Low
99	High
NULL	Low

