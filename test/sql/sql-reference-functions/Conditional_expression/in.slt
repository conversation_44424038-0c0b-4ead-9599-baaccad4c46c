query T
SELECT 1 IN (1, 2, 3) AS RESULT
----
TRUE

query T
SELECT 4 NOT IN (1, 2, 3) AS RESULT
----
TRUE

query T
SELECT 'a' IN (
    SELECT column1 FROM VALUES ('b'), ('c'), ('d')
    ) AS RESULT
----
<PERSON><PERSON>E

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE my_table (col_1 INTEGER, col_2 INTEGER, col_3 INTEGER);

exclude-from-coverage
statement ok
INSERT INTO my_table (col_1, col_2, col_3) VALUES
    (1, 1, 1),
    (1, 2, 3),
    (4, 5, NULL);

query TTT
SELECT col_1, col_2, col_3
    FROM my_table
    WHERE (col_1) IN (1, 10, 100, 1000)
    ORDER BY col_1, col_2, col_3
----
1	1	1
1	2	3

query TTT
SELECT col_1, col_2, col_3
    FROM my_table
    WHERE (col_1, col_2, col_3) IN ( 
                                   (1,2,3), 
                                   (4,5,6)
                                   )
----
1	2	3

query T
SELECT (1, 2, 3) IN (
    SELECT col_1, col_2, col_3 FROM my_table
    ) AS RESULT
----
TRUE

query T
SELECT NULL IN (1, 2, NULL) AS RESULT
----
NULL

query T
SELECT (4, 5, NULL) IN ( (4, 5, NULL), (7, 8, 9) ) AS RESULT
----
NULL

