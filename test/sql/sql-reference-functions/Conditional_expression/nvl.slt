exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE suppliers (
  supplier_id INT PRIMARY KEY,
  supplier_name VARCHA<PERSON>(30),
  phone_region_1 VARCHAR(15),
  phone_region_2 VARCHAR(15));

exclude-from-coverage
statement ok
INSERT INTO suppliers(supplier_id, supplier_name, phone_region_1, phone_region_2)
  VALUES(1, 'Company_ABC', NULL, '555-01111'),
        (2, 'Company_DEF', '555-01222', NULL),
        (3, 'Company_HIJ', '555-01333', '555-01444'),
        (4, 'Company_KLM', NULL, NULL);

query TTTTTT
SELECT supplier_id,
       supplier_name,
       phone_region_1,
       phone_region_2,
       NVL(phone_region_1, phone_region_2) AS IF_REGION_1_NULL,
       NVL(phone_region_2, phone_region_1) AS IF_REGION_2_NULL
  FROM suppliers
  ORDER BY supplier_id
----
1	Company_ABC	NULL	555-01111	555-01111	555-01111
2	Company_DEF	555-01222	NULL	555-01222	555-01222
3	Company_HIJ	555-01333	555-01444	555-01333	555-01444
4	Company_KLM	NULL	NULL	NULL	NULL

