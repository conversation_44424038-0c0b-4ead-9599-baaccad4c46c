exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE demo_table_1_with_nulls (province VARCHAR, created_date DATE);

exclude-from-coverage
statement ok
INSERT INTO demo_table_1_with_nulls (province, created_date) VALUES
  ('Manitoba', '2024-01-18'::DATE),
  ('British Columbia', NULL),
  ('Alberta', '2024-01-19'::DATE),
  (NULL, '2024-01-20'::DATE);

query TT
SELECT OBJECT_CONSTRUCT(*) AS oc,
       OBJECT_CONSTRUCT_KEEP_NULL(*) AS oc_keep_null
  FROM demo_table_1_with_nulls
  ORDER BY oc_keep_null['PROVINCE']
----
'{"CREATED_DATE":"2024-01-19","PROVINCE":"Alberta"}'	'{"CREATED_DATE":"2024-01-19","PROVINCE":"Alberta"}'
'{"PROVINCE":"British Columbia"}'	'{"CREATED_DATE":null,"PROVINCE":"British Columbia"}'
'{"CREATED_DATE":"2024-01-18","PROVINCE":"Manitoba"}'	'{"CREATED_DATE":"2024-01-18","PROVINCE":"Manitoba"}'
'{"CREATED_DATE":"2024-01-20"}'	'{"CREATED_DATE":"2024-01-20","PROVINCE":null}'

query TTT
SELECT OBJECT_CONSTRUCT('key_1', 'one', 'key_2', NULL) AS WITHOUT_KEEP_NULL,
       OBJECT_CONSTRUCT_KEEP_NULL('key_1', 'one', 'key_2', NULL) AS KEEP_NULL_1,
       OBJECT_CONSTRUCT_KEEP_NULL('key_1', 'one', NULL, 'two') AS KEEP_NULL_2
----
'{"key_1":"one"}'	'{"key_1":"one","key_2":null}'	'{"key_1":"one"}'

