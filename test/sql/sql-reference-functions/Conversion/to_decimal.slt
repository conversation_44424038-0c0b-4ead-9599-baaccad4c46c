exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE number_conv(expr VARCHAR);

exclude-from-coverage
statement ok
INSERT INTO number_conv VALUES ('12.3456'), ('98.76546');

statement error
SELECT expr, TO_NUMBER(expr, 10, 9) FROM number_conv
----
100039 (22003): Numeric value '12.3456' is out of range

query TTTT
SELECT column1,
       TO_DECIMAL(column1, '99.9') as D0,
       TO_DECIMAL(column1, '99.9', 9, 5) as D5,
       TO_DECIMAL(column1, 'TM9', 9, 5) as TD5
  FROM VALUES ('1.0'), ('-12.3'), ('0.0'), ('- 0.1')
----
1.0	1	1.00000	1.00000
-12.3	-12	-12.30000	-12.30000
0.0	0	0.00000	0.00000
- 0.1	0	-0.10000	-0.10000

query TT
SELECT column1,
       TO_DECIMAL(column1, '9,999.99', 6, 2) as convert_number
  FROM VALUES ('3,741.72')
----
3,741.72	3741.72

query TT
SELECT column1,
       TO_DECIMAL(column1, '$9,999.99', 6, 2) as convert_currency
  FROM VALUES ('$3,741.72')
----
$3,741.72	3741.72

query T
SELECT TO_DECIMAL('ae5', 'XXX')
----
2789

statement error
SELECT TO_DECIMAL('ae5', 'XX')
----
100140 (22007): Can't parse 'ae5' as number with format 'XX'

