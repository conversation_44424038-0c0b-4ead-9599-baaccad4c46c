exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE strings (v VARCHAR, hex_encoded_string VARCHAR, b BINARY);

exclude-from-coverage
statement ok
INSERT INTO strings (v) VALUES
    ('01'),
    ('A B'),
    ('Hello'),
    (NULL);

statement ok
UPDATE strings SET hex_encoded_string = HEX_ENCODE(v);

statement ok
UPDATE strings SET b = TRY_TO_BINARY(hex_encoded_string, 'HEX');

query TTT
SELECT v, hex_encoded_string, TO_VARCHAR(b, 'UTF-8')
  FROM strings
  ORDER BY v
----
01	3031	01
A B	412042	A B
Hello	48656C6C6F	Hello
NULL	NULL	NULL

