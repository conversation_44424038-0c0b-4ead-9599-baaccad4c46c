exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE departments (
  department_id INTEGER,
  department_name STRING
);

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE employees (
  employee_id INTEGER,
  first_name STRING,
  last_name STRING,
  department_id INTEGER
);

exclude-from-coverage
statement ok
INSERT INTO departments (department_id, department_name)
VALUES
  (101, 'Engineering'),
  (102, 'Marketing'),
  (103, 'HR');

exclude-from-coverage
statement ok
INSERT INTO employees (employee_id, first_name, last_name, department_id)
VALUES
  (1, '<PERSON>', '<PERSON>', 101),
  (2, '<PERSON>', '<PERSON>', 101);

query T
SELECT department_id
  FROM departments d
  WHERE d.department_id != ALL (
    SELECT e.department_id
      FROM employees e);
----
102
103

# Test ANY/ALL with subquery returning no rows

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE subq_test(x INT);

exclude-from-coverage
statement ok
INSERT INTO subq_test VALUES (5),(10);

query T
SELECT 1
WHERE 5 > ALL(SELECT x FROM subq_test WHERE x < 0);
----
1

query T
SELECT 1
WHERE 5 < ANY(SELECT x FROM subq_test WHERE x < 0);
----

# Test ANY/ALL with subquery containing NULL

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE subq_null(x INT);

exclude-from-coverage
statement ok
INSERT INTO subq_null VALUES (NULL);

query T
SELECT 1
WHERE 5 > ALL(SELECT x FROM subq_null);
----

query T
SELECT 1
WHERE 5 < ANY(SELECT x FROM subq_null);
----
