exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE desc_example(
  c1 INT PRIMARY KEY,
  c2 INT,
  c3 INT UNIQUE,
  c4 VARCHAR(30) DEFAULT 'Not applicable' COMMENT 'This column is rarely populated',
  c5 VARCHAR(100));

query TTTTTTTTTTTT
DESCRIBE TABLE desc_example
----
C1	NUMBER(38,0)	COLUMN	N	NULL	Y	N	NULL	NULL	NULL	NULL	NULL
C2	NUMBER(38,0)	COLUMN	Y	NULL	N	N	NULL	NULL	NULL	NULL	NULL
C3	NUMBER(38,0)	COLUMN	Y	NULL	N	Y	NULL	NULL	NULL	NULL	NULL
C4	VARCHAR(30)	COLUMN	Y	'Not applicable'	N	N	NULL	NULL	This column is rarely populated	NULL	NULL
C5	VARCHAR(100)	COLUMN	Y	NULL	N	N	NULL	NULL	NULL	NULL	NULL

# Test DESCRIBE on a view

statement ok
CREATE OR REPLACE VIEW my_view AS SELECT c1, c2 FROM desc_example;

statement ok
DESCRIBE VIEW my_view;

