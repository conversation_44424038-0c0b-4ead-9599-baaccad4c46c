exclude-from-coverage
statement ok
USE SCHEMA snowflake_sample_data.tpch_sf100;

query TTTTT
SHOW TERSE TABLES IN tpch_sf100 STARTS WITH 'LINE'
----
<REGEX>:'.*'	LINES	TABLE	SNOWFLAKE_SAMPLE_DATA	TPCH_SF100

query TTTTT
SHOW TERSE TABLES LIKE '%AGGR%' IN tpch_sf100
----
<REGEX>:'.*'	AGGR	TABLE	SNOWFLAKE_SAMPLE_DATA	TPCH_SF100
<REGEX>:'.*'	AGGR2	TABLE	SNOWFLAKE_SAMPLE_DATA	TPCH_SF100

query TTTTT
SHOW TERSE TABLES IN tpch_sf100 LIMIT 3 FROM 'J'
----
<REGEX>:'.*'	JDEMO1	TABLE	SNOWFLAKE_SAMPLE_DATA	TPCH_SF100
<REGEX>:'.*'	JDEMO2	TABLE	SNOWFLAKE_SAMPLE_DATA	TPCH_SF100
<REGEX>:'.*'	JDEMO3	TABLE	SNOWFLAKE_SAMPLE_DATA	TPCH_SF100

exclude-from-coverage
statement ok
USE SCHEMA COMPATIBILITY_TESTS.PUBLIC;

# Test that SHOW TABLES does not list views

exclude-from-coverage
statement ok
USE SCHEMA PUBLIC;

exclude-from-coverage
statement ok
CREATE OR REPLACE VIEW dummy_view AS SELECT 1;

statement ok
SHOW TABLES;

query I
SELECT COUNT(*) FROM TABLE(result_scan(last_query_id()))
WHERE "name"='DUMMY_VIEW';
----
0