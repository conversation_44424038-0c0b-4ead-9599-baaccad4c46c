exclude-from-coverage
statement ok
create or replace table dt_test (n1 number default 5, n2_int integer default n1+5, n3_bigint bigint autoincrement, n4_dec decimal identity (1,10),
                                 f1 float, f2_double double, f3_real real,
                                 s1 string, s2_var varchar, s3_char char, s4_text text,
                                 b1 binary, b2_var varbinary,
                                 bool1 boolean,
                                 d1 date,
                                 t1 time,
                                 ts1 timestamp, ts2_ltz timestamp_ltz, ts3_ntz timestamp_ntz, ts4_tz timestamp_tz)

query TTTTTTTTTTTT
show columns in table dt_test
----
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	N1	'{"type":"FIXED","precision":38,"scale":0,"nullable":true}'	true	5	COLUMN	''	''	EMBUCKET	''	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	N2_INT	'{"type":"FIXED","precision":38,"scale":0,"nullable":true}'	true	DT_TEST.N1 + 5	COLUMN	''	''	EMBUCKET	''	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	N3_BIGINT	'{"type":"FIXED","precision":38,"scale":0,"nullable":true}'	true	''	COLUMN	''	''	EMBUCKET	IDENTITY START 1 INCREMENT 1 NOORDER	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	N4_DEC	'{"type":"FIXED","precision":38,"scale":0,"nullable":true}'	true	''	COLUMN	''	''	EMBUCKET	IDENTITY START 1 INCREMENT 10 NOORDER	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	F1	'{"type":"REAL","nullable":true}'	true	''	COLUMN	''	''	EMBUCKET	''	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	F2_DOUBLE	'{"type":"REAL","nullable":true}'	true	''	COLUMN	''	''	EMBUCKET	''	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	F3_REAL	'{"type":"REAL","nullable":true}'	true	''	COLUMN	''	''	EMBUCKET	''	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	S1	'{"type":"TEXT","length":16777216,"byteLength":16777216,"nullable":true,"fixed":false}'	true	''	COLUMN	''	''	EMBUCKET	''	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	S2_VAR	'{"type":"TEXT","length":16777216,"byteLength":16777216,"nullable":true,"fixed":false}'	true	''	COLUMN	''	''	EMBUCKET	''	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	S3_CHAR	'{"type":"TEXT","length":1,"byteLength":4,"nullable":true,"fixed":false}'	true	''	COLUMN	''	''	EMBUCKET	''	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	S4_TEXT	'{"type":"TEXT","length":16777216,"byteLength":16777216,"nullable":true,"fixed":false}'	true	''	COLUMN	''	''	EMBUCKET	''	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	B1	'{"type":"BINARY","length":8388608,"byteLength":8388608,"nullable":true,"fixed":true}'	true	''	COLUMN	''	''	EMBUCKET	''	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	B2_VAR	'{"type":"BINARY","length":8388608,"byteLength":8388608,"nullable":true,"fixed":false}'	true	''	COLUMN	''	''	EMBUCKET	''	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	BOOL1	'{"type":"BOOLEAN","nullable":true}'	true	''	COLUMN	''	''	EMBUCKET	''	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	D1	'{"type":"DATE","nullable":true}'	true	''	COLUMN	''	''	EMBUCKET	''	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	T1	'{"type":"TIME","precision":0,"scale":9,"nullable":true}'	true	''	COLUMN	''	''	EMBUCKET	''	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	TS1	'{"type":"TIMESTAMP_NTZ","precision":0,"scale":9,"nullable":true}'	true	''	COLUMN	''	''	EMBUCKET	''	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	TS2_LTZ	'{"type":"TIMESTAMP_LTZ","precision":0,"scale":9,"nullable":true}'	true	''	COLUMN	''	''	EMBUCKET	''	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	TS3_NTZ	'{"type":"TIMESTAMP_NTZ","precision":0,"scale":9,"nullable":true}'	true	''	COLUMN	''	''	EMBUCKET	''	NULL
DT_TEST	<REGEX>:PUBLIC(_[0-9]+)?	TS4_TZ	'{"type":"TIMESTAMP_TZ","precision":0,"scale":9,"nullable":true}'	true	''	COLUMN	''	''	EMBUCKET	''	NULL

# Test SHOW COLUMNS on a non-existent table (should error)

statement error
SHOW COLUMNS IN missing_table;
----
002003 (42S02): SQL compilation error:
Table 'MISSING_TABLE' does not exist or not authorized.