exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE departments (department_id INTEGER, name <PERSON><PERSON><PERSON><PERSON>);

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE employees (employee_ID INTEGER, last_name VARCHA<PERSON>, 
                        department_ID INTEGER, project_names ARRAY);

exclude-from-coverage
statement ok
INSERT INTO departments (department_ID, name) VALUES 
    (1, 'Engineering'), 
    (2, 'Support');

exclude-from-coverage
statement ok
INSERT INTO employees (employee_ID, last_name, department_ID) VALUES 
    (101, '<PERSON>', 1),
    (102, '<PERSON><PERSON>',  1),
    (103, '<PERSON>',  2);

query TTTTTT
SELECT * 
    FROM departments AS d, LATERAL (SELECT * FROM employees AS e WHERE e.department_ID = d.department_ID) AS iv2
    ORDER BY employee_ID
----
1	Engineering	101	Richards	1	NULL
1	Engineering	102	Paulson	1	NULL
2	Support	103	Johnson	2	NULL

exclude-from-coverage
statement ok
CREATE OR REPLACE TEMP TABLE table1 AS SELECT 1 AS dummy;

exclude-from-coverage
query TT
UPDATE employees SET project_names = ARRAY_CONSTRUCT('Materialized Views', 'UDFs') 
    WHERE employee_ID = 101
----
1	0

exclude-from-coverage
query TT
UPDATE employees SET project_names = ARRAY_CONSTRUCT('Materialized Views', 'Lateral Joins')
    WHERE employee_ID = 102
----
1	0

query TTTT
SELECT emp.employee_ID, emp.last_name, index, value AS project_name
    FROM employees AS emp, LATERAL FLATTEN(INPUT => emp.project_names) AS proj_names
    ORDER BY employee_ID
----
101	Richards	0	"Materialized Views"
101	Richards	1	"UDFs"
102	Paulson	0	"Materialized Views"
102	Paulson	1	"Lateral Joins"

query TTTTTT
SELECT * 
    FROM departments AS d INNER JOIN LATERAL (SELECT * FROM employees AS e WHERE e.department_ID = d.department_ID) AS iv2
    ORDER BY employee_ID
----
1	Engineering	101	Richards	1	'["Materialized Views","UDFs"]'
1	Engineering	102	Paulson	1	'["Materialized Views","Lateral Joins"]'
2	Support	103	Johnson	2	NULL

# Test LATERAL subquery with LEFT JOIN returning no rows from subquery

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE t_main(id INT, val STRING);

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE t_sub(x INT);

exclude-from-coverage
statement ok
INSERT INTO t_main VALUES (1,'A'),(2,'B');

exclude-from-coverage
statement ok
INSERT INTO t_sub VALUES (1);

query TT
SELECT m.id, sub.x
FROM t_main AS m
LEFT JOIN LATERAL (
  SELECT x FROM t_sub
  WHERE t_sub.x = m.id AND x < 0
) AS sub ON TRUE
ORDER BY m.id;
----
1	NULL
2	NULL
