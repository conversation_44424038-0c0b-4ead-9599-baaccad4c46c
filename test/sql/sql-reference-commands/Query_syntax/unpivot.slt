exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE monthly_sales(
  empid INT,
  dept TEXT,
  jan INT,
  feb INT,
  mar INT,
  apr INT);

exclude-from-coverage
statement ok
INSERT INTO monthly_sales VALUES
  (1, 'electronics', 100, 200, 300, 100),
  (2, 'clothes', 100, 300, 150, 200),
  (3, 'cars', 200, 400, 100, 50),
  (4, 'appliances', 100, NULL, 100, 50);

query TTTT
SELECT *
  FROM monthly_sales
    UNPIVOT (sales FOR month IN (jan, feb, mar, apr))
  ORDER BY empid
----
1	electronics	JAN	100
1	electronics	FEB	200
1	electronics	MAR	300
1	electronics	APR	100
2	clothes	JAN	100
2	clothes	FEB	300
2	clothes	MAR	150
2	clothes	APR	200
3	cars	JAN	200
3	cars	FEB	400
3	cars	MAR	100
3	cars	APR	50
4	appliances	JAN	100
4	appliances	MAR	100
4	appliances	APR	50

query TTTT
SELECT *
  FROM monthly_sales
    UNPIVOT INCLUDE NULLS (sales FOR month IN (jan, feb, mar, apr))
  ORDER BY empid
----
1	electronics	JAN	100
1	electronics	FEB	200
1	electronics	MAR	300
1	electronics	APR	100
2	clothes	JAN	100
2	clothes	FEB	300
2	clothes	MAR	150
2	clothes	APR	200
3	cars	JAN	200
3	cars	FEB	400
3	cars	MAR	100
3	cars	APR	50
4	appliances	JAN	100
4	appliances	FEB	NULL
4	appliances	MAR	100
4	appliances	APR	50

query TTT
SELECT dept, month, sales
  FROM monthly_sales
    UNPIVOT INCLUDE NULLS (sales FOR month IN (jan, feb, mar, apr))
  ORDER BY dept
----
appliances	JAN	100
appliances	FEB	NULL
appliances	MAR	100
appliances	APR	50
cars	JAN	200
cars	FEB	400
cars	MAR	100
cars	APR	50
clothes	JAN	100
clothes	FEB	300
clothes	MAR	150
clothes	APR	200
electronics	JAN	100
electronics	FEB	200
electronics	MAR	300
electronics	APR	100

# Test UNPIVOT excluding completely NULL values

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE unp_test(id INT, attr1 INT, attr2 INT);

exclude-from-coverage
statement ok
INSERT INTO unp_test VALUES (1, 10, 20), (2, NULL, NULL);

query TTT
SELECT id, attr, val
FROM unp_test
UNPIVOT( val FOR attr IN (attr1, attr2) ) AS u
ORDER BY id, attr;
----
1	ATTR1	10
1	ATTR2	20
