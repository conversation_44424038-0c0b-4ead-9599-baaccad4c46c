exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE quarterly_sales(
  empid INT,
  amount INT,
  quarter TEXT)
  AS SELECT * FROM VALUES
    (1, 10000, '2023_Q1'),
    (1, 400, '2023_Q1'),
    (2, 4500, '2023_Q1'),
    (2, 35000, '2023_Q1'),
    (1, 5000, '2023_Q2'),
    (1, 3000, '2023_Q2'),
    (2, 200, '2023_Q2'),
    (2, 90500, '2023_Q2'),
    (1, 6000, '2023_Q3'),
    (1, 5000, '2023_Q3'),
    (2, 2500, '2023_Q3'),
    (2, 9500, '2023_Q3'),
    (3, 2700, '2023_Q3'),
    (1, 8000, '2023_Q4'),
    (1, 10000, '2023_Q4'),
    (2, 800, '2023_Q4'),
    (2, 4500, '2023_Q4'),
    (3, 2700, '2023_Q4'),
    (3, 16000, '2023_Q4'),
    (3, 10200, '2023_Q4');

query TTTTT
SELECT *
  FROM quarterly_sales
    PIVOT(SUM(amount) FOR quarter IN (ANY ORDER BY quarter))
  ORDER BY empid
----
1	10400	8000	11000	18000
2	39500	90700	12000	5300
3	NULL	NULL	2700	28900

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE ad_campaign_types_by_quarter(
  quarter VARCHAR,
  television BOOLEAN,
  radio BOOLEAN,
  print BOOLEAN)
  AS SELECT * FROM VALUES
    ('2023_Q1', TRUE, FALSE, FALSE),
    ('2023_Q2', FALSE, TRUE, TRUE),
    ('2023_Q3', FALSE, TRUE, FALSE),
    ('2023_Q4', TRUE, FALSE, TRUE);

query TTT
SELECT *
  FROM quarterly_sales
    PIVOT(SUM(amount) FOR quarter IN (
      SELECT DISTINCT quarter
        FROM ad_campaign_types_by_quarter
        WHERE television = TRUE
        ORDER BY quarter))
  ORDER BY empid
----
1	10400	18000
2	39500	5300
3	NULL	28900

query TTTTTT
SELECT 'Average sale amount' AS aggregate, *
  FROM quarterly_sales
    PIVOT(AVG(amount) FOR quarter IN (ANY ORDER BY quarter))
UNION
SELECT 'Highest value sale' AS aggregate, *
  FROM quarterly_sales
    PIVOT(MAX(amount) FOR quarter IN (ANY ORDER BY quarter))
UNION
SELECT 'Lowest value sale' AS aggregate, *
  FROM quarterly_sales
    PIVOT(MIN(amount) FOR quarter IN (ANY ORDER BY quarter))
UNION
SELECT 'Number of sales' AS aggregate, *
  FROM quarterly_sales
    PIVOT(COUNT(amount) FOR quarter IN (ANY ORDER BY quarter))
UNION
SELECT 'Total amount' AS aggregate, *
  FROM quarterly_sales
    PIVOT(SUM(amount) FOR quarter IN (ANY ORDER BY quarter))
ORDER BY aggregate, empid
----
Average sale amount	1	5200.000000	4000.000000	5500.000000	9000.000000
Average sale amount	2	19750.000000	45350.000000	6000.000000	2650.000000
Average sale amount	3	NULL	NULL	2700.000000	9633.333333
Highest value sale	1	10000.000000	5000.000000	6000.000000	10000.000000
Highest value sale	2	35000.000000	90500.000000	9500.000000	4500.000000
Highest value sale	3	NULL	NULL	2700.000000	16000.000000
Lowest value sale	1	400.000000	3000.000000	5000.000000	8000.000000
Lowest value sale	2	4500.000000	200.000000	2500.000000	800.000000
Lowest value sale	3	NULL	NULL	2700.000000	2700.000000
Number of sales	1	2.000000	2.000000	2.000000	2.000000
Number of sales	2	2.000000	2.000000	2.000000	2.000000
Number of sales	3	0.000000	0.000000	1.000000	3.000000
Total amount	1	10400.000000	8000.000000	11000.000000	18000.000000
Total amount	2	39500.000000	90700.000000	12000.000000	5300.000000
Total amount	3	NULL	NULL	2700.000000	28900.000000

exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE emp_manager(
    empid INT,
    managerid INT)
  AS SELECT * FROM VALUES
    (1, 7),
    (2, 8),
    (3, 9);

query TTTTTT
WITH
  src AS
  (
    SELECT *
      FROM quarterly_sales
        PIVOT(SUM(amount) FOR quarter IN (ANY ORDER BY quarter))
  )
SELECT em.managerid, src.*
  FROM emp_manager em
  JOIN src ON em.empid = src.empid
  ORDER BY empid
----
7	1	10400	8000	11000	18000
8	2	39500	90700	12000	5300
9	3	NULL	NULL	2700	28900

query TTTT
SELECT *
  FROM quarterly_sales
    PIVOT(SUM(amount) FOR quarter IN (
      '2023_Q1',
      '2023_Q2',
      '2023_Q3'))
  ORDER BY empid
----
1	10400	8000	11000
2	39500	90700	12000
3	NULL	NULL	2700

query TTTTT
SELECT *
  FROM quarterly_sales
    PIVOT(SUM(amount) FOR quarter IN (
      '2023_Q1',
      '2023_Q2',
      '2023_Q3',
      '2023_Q4'))
  ORDER BY empid
----
1	10400	8000	11000	18000
2	39500	90700	12000	5300
3	NULL	NULL	2700	28900

query TTTTT
SELECT *
  FROM quarterly_sales
    PIVOT(SUM(amount) FOR quarter IN (
      '2023_Q1',
      '2023_Q2',
      '2023_Q3',
      '2023_Q4')) AS p (employee, q1, q2, q3, q4)
  ORDER BY employee
----
1	10400	8000	11000	18000
2	39500	90700	12000	5300
3	NULL	NULL	2700	28900

query TTTTT
SELECT empid,
       "'2023_Q1'" AS q1,
       "'2023_Q2'" AS q2,
       "'2023_Q3'" AS q3,
       "'2023_Q4'" AS q4
  FROM quarterly_sales
    PIVOT(sum(amount) FOR quarter IN (
      '2023_Q1',
      '2023_Q2',
      '2023_Q3',
      '2023_Q4'))
  ORDER BY empid
----
1	10400	8000	11000	18000
2	39500	90700	12000	5300
3	NULL	NULL	2700	28900

query TTTTT
SELECT *
  FROM quarterly_sales
    PIVOT(SUM(amount) FOR quarter IN (ANY ORDER BY quarter)
      DEFAULT ON NULL (0))
  ORDER BY empid
----
1	10400	8000	11000	18000
2	39500	90700	12000	5300
3	0	0	2700	28900

query TTT
SELECT *
  FROM quarterly_sales
    PIVOT(SUM(amount)
      FOR quarter IN (
        '2023_Q1',
        '2023_Q2')
      DEFAULT ON NULL (0))
  ORDER BY empid
----
1	10400	8000
2	39500	90700
3	0	0

exclude-from-coverage
statement ok
ALTER TABLE quarterly_sales ADD COLUMN discount_percent INT DEFAULT 0;

exclude-from-coverage
query TT
UPDATE quarterly_sales SET discount_percent = 2;  -- using fixed value instead of random (discount_percent = UNIFORM(0, 5, RANDOM()))
----
20	0

query TTTTT
WITH
  sales_without_discount AS
    (SELECT * EXCLUDE(discount_percent) FROM quarterly_sales)
SELECT *
  FROM sales_without_discount
    PIVOT(SUM(amount) FOR quarter IN (ANY ORDER BY quarter))
  ORDER BY empid
----
1	10400	8000	11000	18000
2	39500	90700	12000	5300
3	NULL	NULL	2700	28900

query TTTTT
WITH
  sales_without_amount AS
    (SELECT * EXCLUDE(amount) FROM quarterly_sales)
SELECT *
  FROM sales_without_amount
    PIVOT(AVG(discount_percent) FOR quarter IN (ANY ORDER BY quarter))
  ORDER BY empid
----
1	2.000000	2.000000	2.000000	2.000000
2	2.000000	2.000000	2.000000	2.000000
3	NULL	NULL	2.000000	2.000000

query TTTTTTTT
SELECT SUM(q1_sales) AS q1_sales_total,
       SUM(q2_sales) AS q2_sales_total,
       SUM(q3_sales) AS q3_sales_total,
       SUM(q4_sales) AS q4_sales_total,
       MAX(q1_discount) AS q1_maximum_discount,
       MAX(q2_discount) AS q2_maximum_discount,
       MAX(q3_discount) AS q3_maximum_discount,
       MAX(q4_discount) AS q4_maximum_discount
  FROM
    (SELECT amount,
            quarter AS quarter_amount,
            quarter AS quarter_discount,
            discount_percent
      FROM quarterly_sales)
  PIVOT (
    SUM(amount)
    FOR quarter_amount IN (
      '2023_Q1',
      '2023_Q2',
      '2023_Q3',
      '2023_Q4'))
  PIVOT (
    MAX(discount_percent)
    FOR quarter_discount IN (
      '2023_Q1',
      '2023_Q2',
      '2023_Q3',
      '2023_Q4'))
  AS pivoted_data (
    q1_sales, q2_sales, q3_sales, q4_sales,
    q1_discount, q2_discount, q3_discount, q4_discount
  )
----
49900	98700	25700	52200	2	2	2	2

