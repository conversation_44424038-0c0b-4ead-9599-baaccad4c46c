exclude-from-coverage
statement ok
CREATE OR REPLACE TABLE table1 AS SELECT 1 AS column1;

statement ok
SET V1 = 10;

query T
SELECT $V1;
----
10

statement ok
SET V2 = 'example';

query T
SELECT $V2;
----
example

statement ok
SET (V1, V2) = (10, 'example');

query T
SELECT $V1;
----
10

query T
SELECT $V2;
----
example

statement ok
SET id_threshold = (SELECT COUNT(*)/2 FROM table1);

query T
SELECT $id_threshold;
----
0.5

statement ok
SET (min, max) = (40, 70);

statement ok
SET (min, max) = (50, 2 * $min);

query T
SELECT $max;
----
80

# Test selecting an unset session variable (should error)

statement error
SELECT $undefined_var;
----
002211 (02000): SQL compilation error: error line 1 at position 7
Session variable '$UNDEFINED_VAR' does not exist

# Test reassigning a session variable to a new type

statement ok
SET var1 = 100;

statement ok
SET var1 = 'changed';

query T
SELECT $var1;
----
changed