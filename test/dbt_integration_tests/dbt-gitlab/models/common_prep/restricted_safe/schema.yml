version: 2

models:
  - name: prep_charge
    description: '{{ doc("prep_charge") }}'
    columns:
      - name: dim_charge_id
        description: The unique identifier of the rate plan charge.
        data_tests:
          - not_null
          - unique
      - name: dim_date_id
        description: The identifier of date month in dim_date
      - name: dim_subscription_id
        description: The identifier of subscription in dim_subscriptions table
        data_tests:
          - not_null
      - name: dim_product_detail_id
        description: The identifier of product details in dim_product_detail
        data_tests:
          - not_null
      - name: dim_billing_account_id
        description: Zuora account id
      - name:  dim_crm_account_id
        description: account id frm crm
      - name: mrr
        description: Monthly Recurring Revenue value for given month
      - name: arr
        description: Annual Recurring Revenue value for given month
      - name: quantity
        description: Total quantity
      - name: extended_list_price
        description: Extended List Price
  
  - name: prep_charge_mrr
    description: '{{ doc("prep_charge_mrr") }}'
    columns:
      - name: dim_charge_id
        description: The unique identifier of the rate plan charge.
        data_tests:
          - not_null
          - unique

  - name: prep_charge_mrr_daily
    description: '{{ doc("prep_charge_mrr_daily") }}'
    data_tests:
      - dbt_utils.unique_combination_of_columns:
            combination_of_columns:
              - dim_charge_id
              - date_actual
    columns:
      - name: date_actual
        description: Date the charge/mrr was effective
      - name: subscription_name
        description: ' {{ doc("subscription_name") }} ' 
      - name: subscription_name_slugify
        description: ' {{ doc("original_subscription_name_slugify") }} '
      - name: subscription_version
        description: ' {{ doc("subscription_version") }} '
      - name: subscription_created_by_id
        description: User id for the person who created the subscription
      - name: rate_plan_charge_number
        description: ' {{ doc("rate_plan_charge_number") }} '
      - name: rate_plan_charge_version
        description: ' {{ doc("rate_plan_charge_version") }} '
      - name: rate_plan_charge_segment
        description: ' {{ doc("rate_plan_charge_segment") }} '
      - name: dim_charge_id
        description: ' {{ doc("dim_charge_id") }} '
      - name: dim_product_detail_id
        description: ' {{ doc("dim_product_detail_id") }} '
      - name: dim_amendment_id_charge
        description: ' {{ doc("dim_amendment_id_charge") }} '
      - name: dim_subscription_id
        description: ' {{ doc("dim_subscription_id") }} '
      - name: dim_billing_account_id
        description: ' {{ doc("dim_billing_account_id") }} '
      - name: dim_crm_account_id
        description: ' {{ doc("dim_crm_account_id") }} '
      - name: dim_parent_crm_account_id
        description: ' {{ doc("dim_parent_crm_account_id") }} '
      - name: dim_order_id
        description: ' {{ doc("dim_order_id") }} '
      - name: effective_start_date_id
        description: ' {{ doc("effective_start_date_id") }} '
      - name: effective_end_date_id
        description: ' {{ doc("effective_end_date_id") }} '
      - name: subscription_status
        description: ' {{ doc("subscription_status") }} '
      - name: rate_plan_name
        description: ' {{ doc("rate_plan_name") }} '
      - name: rate_plan_charge_name
        description: ' {{ doc("rate_plan_charge_name") }} '
      - name: rate_plan_charge_description
        description: ' {{ doc("rate_plan_charge_description") }} '
      - name: is_last_segment
        description: ' {{ doc("is_last_segment") }} '
      - name: discount_level
        description: ' {{ doc("discount_level") }} '
      - name: charge_type
        description: ' {{ doc("charge_type") }} '
      - name: rate_plan_charge_amendement_type
        description: ' {{ doc("rate_plan_charge_amendement_type") }} '
      - name: unit_of_measure
        description: ' {{ doc("unit_of_measure") }} '
      - name: is_paid_in_full
        description: ' {{ doc("is_paid_in_full") }} '
      - name: months_of_future_billings
        description: ' {{ doc("months_of_future_billings") }} '
      - name: is_included_in_arr_calc
        description: ' {{ doc("is_included_in_arr_calc") }} '
      - name: subscription_start_date
        description: ' {{ doc("subscription_start_date") }} '
      - name: subscription_end_date
        description: ' {{ doc("subscription_end_date") }} '
      - name: effective_start_date
        description: ' {{ doc("effective_start_date") }} '
      - name: effective_end_date
        description: ' {{ doc("effective_end_date") }} '
      - name: effective_start_month
        description: ' {{ doc("effective_start_month") }} '
      - name: effective_end_month
        description: ' {{ doc("effective_end_month") }} '
      - name: charged_through_date
        description: ' {{ doc("charged_through_date") }} '
      - name: charge_created_date
        description: ' {{ doc("charge_created_date") }} '
      - name: charge_created_datetime
        description: ' {{ doc("charge_created_datetime") }} '
      - name: charge_updated_date
        description: ' {{ doc("charge_updated_date") }} '
      - name: charge_term
        description: ' {{ doc("charge_term") }} '
      - name: billing_period
        description: ' {{ doc("billing_period") }} '
      - name: mrr
        description: ' {{ doc("mrr") }} '
      - name: previous_mrr_calc
        description: ' {{ doc("previous_mrr_calc") }} '
      - name: previous_mrr
        description: ' {{ doc("previous_mrr") }} '
      - name: delta_mrr_calc
        description: ' {{ doc("delta_mrr_calc") }} '
      - name: delta_mrr
        description: ' {{ doc("delta_mrr") }} '
      - name: delta_mrc
        description: ' {{ doc("delta_mrc") }} '
      - name: arr
        description: ' {{ doc("arr") }} '
      - name: previous_arr
        description: ' {{ doc("previous_arr") }} '
      - name: delta_arc
        description: ' {{ doc("delta_arc") }} '
      - name: delta_arr
        description: ' {{ doc("delta_arr") }} '
      - name: list_price
        description: ' {{ doc("list_price") }} '
      - name: extended_list_price
        description: ' {{ doc("extended_list_price") }} '
      - name: quantity
        description: ' {{ doc("quantity") }} '
      - name: previous_quantity_calc
        description: ' {{ doc("previous_quantity_calc") }} '
      - name: previous_quantity
        description: ' {{ doc("previous_quantity") }} '
      - name: delta_quantity_calc
        description: ' {{ doc("delta_quantity_calc") }} '
      - name: delta_quantity
        description: ' {{ doc("delta_quantity") }} '
      - name: tcv
        description: ' {{ doc("tcv") }} '
      - name: delta_tcv
        description: ' {{ doc("delta_tcv") }} '
      - name: estimated_total_future_billings
        description: ' {{ doc("estimated_total_future_billings") }} '
      - name: is_manual_charge
        description: ' {{ doc("is_manual_charge") }} '
      - name: type_of_arr_change
        description: ' {{ doc("type_of_arr_change") }} '
  
  - name: prep_charge_mrr_daily_with_namespace_and_installation
    description: '{{ doc("prep_charge_mrr_daily_with_namespace_and_installation") }}'
    data_tests:
      - dbt_utils.unique_combination_of_columns:
            combination_of_columns:
              - date_actual
              - dim_charge_id
              - dim_subscription_id
              - dim_namespace_id
              - dim_installation_id
    columns:
      - name: date_actual
        description: Date the charge/mrr was effective
      - name: subscription_name
        description: ' {{ doc("subscription_name") }} ' 
      - name: subscription_name_slugify
        description: ' {{ doc("original_subscription_name_slugify") }} '
      - name: subscription_version
        description: ' {{ doc("subscription_version") }} '
      - name: subscription_created_by_id
        description: User id for the person who created the subscription
      - name: rate_plan_charge_number
        description: ' {{ doc("rate_plan_charge_number") }} '
      - name: rate_plan_charge_version
        description: ' {{ doc("rate_plan_charge_version") }} '
      - name: rate_plan_charge_segment
        description: ' {{ doc("rate_plan_charge_segment") }} '
      - name: dim_charge_id
        description: ' {{ doc("dim_charge_id") }} '
      - name: dim_product_detail_id
        description: ' {{ doc("dim_product_detail_id") }} '
      - name: dim_amendment_id_charge
        description: ' {{ doc("dim_amendment_id_charge") }} '
      - name: dim_subscription_id
        description: ' {{ doc("dim_subscription_id") }} '
      - name: dim_billing_account_id
        description: ' {{ doc("dim_billing_account_id") }} '
      - name: dim_crm_account_id
        description: ' {{ doc("dim_crm_account_id") }} '
      - name: dim_parent_crm_account_id
        description: ' {{ doc("dim_parent_crm_account_id") }} '
      - name: dim_order_id
        description: ' {{ doc("dim_order_id") }} '
      - name: effective_start_date_id
        description: ' {{ doc("effective_start_date_id") }} '
      - name: effective_end_date_id
        description: ' {{ doc("effective_end_date_id") }} '
      - name: subscription_status
        description: ' {{ doc("subscription_status") }} '
      - name: rate_plan_name
        description: ' {{ doc("rate_plan_name") }} '
      - name: rate_plan_charge_name
        description: ' {{ doc("rate_plan_charge_name") }} '
      - name: rate_plan_charge_description
        description: ' {{ doc("rate_plan_charge_description") }} '
      - name: is_last_segment
        description: ' {{ doc("is_last_segment") }} '
      - name: discount_level
        description: ' {{ doc("discount_level") }} '
      - name: charge_type
        description: ' {{ doc("charge_type") }} '
      - name: rate_plan_charge_amendement_type
        description: ' {{ doc("rate_plan_charge_amendement_type") }} '
      - name: unit_of_measure
        description: ' {{ doc("unit_of_measure") }} '
      - name: is_paid_in_full
        description: ' {{ doc("is_paid_in_full") }} '
      - name: months_of_future_billings
        description: ' {{ doc("months_of_future_billings") }} '
      - name: is_included_in_arr_calc
        description: ' {{ doc("is_included_in_arr_calc") }} '
      - name: subscription_start_date
        description: ' {{ doc("subscription_start_date") }} '
      - name: subscription_end_date
        description: ' {{ doc("subscription_end_date") }} '
      - name: effective_start_date
        description: ' {{ doc("effective_start_date") }} '
      - name: effective_end_date
        description: ' {{ doc("effective_end_date") }} '
      - name: effective_start_month
        description: ' {{ doc("effective_start_month") }} '
      - name: effective_end_month
        description: ' {{ doc("effective_end_month") }} '
      - name: charged_through_date
        description: ' {{ doc("charged_through_date") }} '
      - name: charge_created_date
        description: ' {{ doc("charge_created_date") }} '
      - name: charge_created_datetime
        description: ' {{ doc("charge_created_datetime") }} '
      - name: charge_updated_date
        description: ' {{ doc("charge_updated_date") }} '
      - name: charge_term
        description: ' {{ doc("charge_term") }} '
      - name: billing_period
        description: ' {{ doc("billing_period") }} '
      - name: mrr
        description: ' {{ doc("mrr") }} '
      - name: previous_mrr_calc
        description: ' {{ doc("previous_mrr_calc") }} '
      - name: previous_mrr
        description: ' {{ doc("previous_mrr") }} '
      - name: delta_mrr_calc
        description: ' {{ doc("delta_mrr_calc") }} '
      - name: delta_mrr
        description: ' {{ doc("delta_mrr") }} '
      - name: delta_mrc
        description: ' {{ doc("delta_mrc") }} '
      - name: arr
        description: ' {{ doc("arr") }} '
      - name: previous_arr
        description: ' {{ doc("previous_arr") }} '
      - name: delta_arc
        description: ' {{ doc("delta_arc") }} '
      - name: delta_arr
        description: ' {{ doc("delta_arr") }} '
      - name: list_price
        description: ' {{ doc("list_price") }} '
      - name: extended_list_price
        description: ' {{ doc("extended_list_price") }} '
      - name: quantity
        description: ' {{ doc("quantity") }} '
      - name: previous_quantity_calc
        description: ' {{ doc("previous_quantity_calc") }} '
      - name: previous_quantity
        description: ' {{ doc("previous_quantity") }} '
      - name: delta_quantity_calc
        description: ' {{ doc("delta_quantity_calc") }} '
      - name: delta_quantity
        description: ' {{ doc("delta_quantity") }} '
      - name: tcv
        description: ' {{ doc("tcv") }} '
      - name: delta_tcv
        description: ' {{ doc("delta_tcv") }} '
      - name: estimated_total_future_billings
        description: ' {{ doc("estimated_total_future_billings") }} '
      - name: is_manual_charge
        description: ' {{ doc("is_manual_charge") }} '
      - name: type_of_arr_change
        description: ' {{ doc("type_of_arr_change") }} '
      - name: dim_installation_id
        description: '{{ doc("dim_installation_id") }}'
      - name: dim_namespace_id
        description: '{{ doc("dim_namespace_id") }}'
  
  - name: prep_crm_account
    description: '{{ doc("prep_crm_account") }}'
    columns:
      - name: dim_crm_account_id
        description: account id from SFDC identifing the customer
      - name: crm_account_name
        description: account name from SFDC
      - name: crm_account_country
        description: billing country of SFDC account
      - name: dim_parent_crm_account_id
        description: ultimate parent account id
      - name: ultimate_parent_account_name
        description: parent account name
      - name: ultimate_parent_account_segment
        description: Sales segment of the parent account
      - name: ultimate_parent_billing_country
        description: billing country of parent account
      - name: record_type_id
      - name: gitlab_com_user
      - name: is_jihu_account
      - name: account_owner
      - name: account_owner_team
      - name: account_type
      - name: gtm_strategy
      - name: technical_account_manager
      - name: is_deleted
        description: flag indicating if account has been deleted
      - name: is_reseller
        description: Identify whether a crm_account is a reseller.
      - name: merged_to_account_id
        description: for deleted accounts this is the SFDC account they were merged to
      - name: crm_account_created_date
        description: The date on which the CRM account was created in SFDC
      - name: gs_health_csm_sentiment 
        description: This is what the TAM thinks the health of this account should be - Formerly was just Health Score - Gainsight is the SSOT for this field and its value can only be updated in Gainsight.
      - name: tam_manager
        description: The name of the manager for the tehchnical account manager who manages the account
      - name: executive_sponsor
        description: executive sponsor of this account


  - name: prep_crm_opportunity
    description: '{{ doc("prep_crm_opportunity") }}'
    data_tests:
      - dbt_utils.unique_combination_of_columns:
            combination_of_columns:
              - dim_crm_opportunity_id
              - snapshot_id
    columns:
      - name: next_steps
        description: Free text field that provides insight into the next step to continue progressing the Opportunity through the Sales Pipeline.
      - name: auto_renewal_status
        descripton: Flag with on/off. Indicates whether a current subscription will automatically renew for the same term as their current subscription after the expiration of the current term
      - name: qsr_notes
        description: '{{ doc("qsr_notes") }}'
      - name: qsr_status  
        description: Quarterly Subscription Reconciliation status (Pending, failed, processed)
      - name: manager_confidence
      - name: renewal_risk_category
        description: Renewal forecasting field indicating whether the customer will renew, churn or contract (actionable or not)
      - name: renewal_swing_arr
        description: The revenue the Account team believes can be saved on an at-risk Renewal if action is taken
      - name: renewal_manager
        description:  Renewals Manager supporting this Opportunity
      - name: renewal_forecast_health
        description: Red, Yellow, Green health rating based on the Net ARR field
      - name: cycle_time_in_days
        description: '{{ doc("cycle_time_in_days") }}'
      - name: ptc_predicted_arr
        description: This field contains the predicted amount of ARR after renewal, generated by a machine learning model when the subscription is within 6 months of renewal date.
      - name: ptc_predicted_renewal_risk_category
        description: This field contains the predicted renewal risk category, generated by a machine learning model when the subscription is within 6 months of renewal date.
      - name: created_arr 
        description: >
          Created ARR associated with the opportunity. The opportunity needs to be eligible, so is_net_arr_pipeline_created needs to be true.
      - name: closed_won_opps
        description: > 
          The closed_won_opps field counts opportunities marked as closed and won (is_closed_won = TRUE) and 
          eligible for win rate calculations (is_win_rate_calc = TRUE), otherwise set to 0. It quantifies successful deal closures.
      - name: closed_opps
        description: >
          The closed_opps field tallies all opportunities eligible for win rate calculations (is_win_rate_calc = TRUE), 
          encompassing both won and lost deals, or sets to 0 if not eligible.
      - name: closed_net_arr
        description: >
          The closed_net_arr field calculates the total Net ARR for deals that closed, regardless of won or loss status
      - name: created_arr_in_snapshot_quarter
        description: >
          This field computes the running sum of Net ARR for opportunities created within the same fiscal quarter as the snapshot, 
          indicated by is_net_arr_pipeline_created = 1, or assigns 0 otherwise.
      - name: closed_won_opps_in_snapshot_quarter
        description: >
          This field counts the number of opportunities that were closed as won (is_closed_won = TRUE) and are eligible for win rate
          calculation (is_win_rate_calc = TRUE) within the same fiscal quarter as the snapshot (snapshot_fiscal_quarter_date = close_fiscal_quarter_date). 
          If these conditions are not met, the value is set to 0. 
      - name: closed_opps_in_snapshot_quarter
        description: >
          This field tallies the number of opportunities that were closed (regardless of being won or lost) and are eligible for win rate calculation 
          (is_win_rate_calc = TRUE) within the snapshot's fiscal quarter (snapshot_fiscal_quarter_date = close_fiscal_quarter_date). 
      - name: closed_net_arr_in_snapshot_quarter
        description: > 
          Calculates the total Net ARR from deals eligible for win rate calculation (is_win_rate_calc = TRUE), 
          using calculated_deal_count multiplied by net_arr. If not eligible, the value is 0. 
      - name: booked_net_arr_in_snapshot_quarter
        description: > 
          This field computes the Net ARR for opportunities within the snapshot quarter (snapshot_fiscal_quarter_date = close_fiscal_quarter_date) 
          that are either won (is_won = 1) or are renewals marked as lost (is_renewal = 1 AND is_lost = 1). 
          It reflects the ARR booked through new wins and renewal scenarios within the quarter.
      - name: created_deals_in_snapshot_quarter
        description: > 
          This field computes the running sum of Net ARR for opportunities created within the same fiscal quarter as the snapshot, 
          indicated by is_net_arr_pipeline_created = 1, or assigns 0 otherwise
      - name: cycle_time_in_days_in_snapshot_quarter
        description: >
          Number of days since created date of opportunity until its closure. For renewal opportunities created date = ARR created date. Only opportunities
          that close within a snapshot quarter
      - name: booked_deal_count_in_snapshot_quarter
        description: >
          This field computes the Net ARR for won opportunities within the snapshot quarter (snapshot_fiscal_quarter_date = close_fiscal_quarter_date) 
          where the fpa_master_bookings flag = TRUE.
      - name: open_1plus_net_arr_in_snapshot_quarter
        description: >
          Open Net ARR that's expecting to close in the snapshot quarter. It should decrease over time as the end of quarter approaches.
      - name: open_1plus_deal_count_in_snapshot_quarter
        description: >
          Open deals that are expecting to close in the snapshot quarter. It should decrease over time as the end of quarter approaches.
      - name: positive_booked_deal_count_in_snapshot_quarter
        description: >
          Opportunities that were won this quarter where the ARR is greater than 0
      - name: positive_booked_net_arr_in_snapshot_quarter
        description: >
          ARR that was won this quarter where the ARR for the opportunity is greater than 0
      - name: positive_open_deal_count_in_snapshot_quarter
        description: >
          Opportunities that are open and expected to close this quarter where the ARR for the opportunity is greater than 0
      - name: positive_open_net_arr_in_snapshot_quarter
        description: >
          ARR that is open and expected to close this quarter where the ARR for the opportunity is greater than 0
      - name: closed_deals_in_snapshot_quarter
        description: >
          Count of all closed opportunities (lost and won)
      - name: closed_net_arr_in_snapshot_quarter
        description: >
          Sum of ARR from all closed opportunities (lost and won)
      - name: sales_qualified_source_live
        description: >
          Sales qualified source from the live data 
      - name: sales_qualified_source_grouped_live
        description: >
          Sales qualified source grouped from the live data 
      - name: is_edu_oss_live
        description: >
          is_edu_oss flag from the live data 
      - name: opportunity_category_live
        description: >
          Opportunity category from the live data 
      - name: is_jihu_account_live
        description: >
          is_jihu_account flag from the live data 
      - name: deal_path_live
        description: >
          Deal Path from the live data 
      - name: parent_crm_account_geo_live
        description: >
          Parent Geo from the live data 
      - name: order_type_live
        description: >
          Order type stamped from the live data 
      - name: order_type_grouped_live
        description: >
          Order type (stamped) grouped from the live data 
      - name: professional_services_value
        description: >
          Amount on an opportunity that is from Professional Services only
      - name: edu_services_value
        description: >
          Amount on an opportunity that is from Educational Services only
      - name: investment_services_value
        description: >
          Amount on an opportunity that is from Investment Services only
      - name: startup_type
        description: Indicates what type of startup opportunity (Seed vs. Early Stage)
      - name: number_of_sa_activity_tasks
        description: Count of SFDC Tasks completed by Solutions Architects on a given Opportunity. 

  - name: prep_crm_opportunity_live
    description: '{{ doc("prep_crm_opportunity_live") }}'
    data_tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - dim_crm_opportunity_id
            - dbt_valid_from
    columns:
      - name: account_id
      - name: account_owner
      - name: acv
      - name: aggregate_partner
      - name: amount
      - name: arr
      - name: arr_basis
      - name: arr_basis_for_clari
      - name: arr_created_date
      - name: arr_created_date_id
      - name: auto_renewal_status
      - name: calculated_discount
      - name: calculation_days_in_stage_date
      - name: channel_type
      - name: closed_buckets
      - name: close_date
      - name: close_date_id
      - name: close_fiscal_year
      - name: competitors
      - name: competitors_amazon_code_commit_flag
      - name: competitors_atlassian_flag
      - name: competitors_aws_flag
      - name: competitors_azure_devops_flag
      - name: competitors_azure_flag
      - name: competitors_bamboo_flag
      - name: competitors_bitbucket_flag
      - name: competitors_bitbucket_server_flag
      - name: competitors_circleci_flag
      - name: competitors_github_enterprise_flag
      - name: competitors_github_flag
      - name: competitors_gitlab_core_flag
      - name: competitors_gitlab_flag
      - name: competitors_jenkins_flag
      - name: competitors_none_flag
      - name: competitors_other_flag
      - name: competitors_perforce_flag
      - name: competitors_svn_flag
      - name: competitors_unknown_flag
      - name: competitors_visual_studio_flag
      - name: comp_channel_neutral
      - name: comp_new_logo_override
      - name: contract_reset_opportunity_id
      - name: cp_champion
      - name: cp_close_plan
      - name: cp_decision_criteria
      - name: cp_decision_process
      - name: cp_economic_buyer
      - name: cp_help
      - name: cp_identify_pain
      - name: cp_metrics
      - name: cp_paper_process
      - name: cp_partner
      - name: cp_review_notes
      - name: cp_risks
      - name: cp_score
      - name: cp_use_cases
      - name: cp_value_driver
      - name: cp_why_do_anything_at_all
      - name: cp_why_gitlab
      - name: cp_why_now
      - name: created_date
      - name: created_date_id
      - name: critical_deal_flag
      - name: crm_account_owner_stamped_name
      - name: crm_business_dev_rep_id
      - name: crm_business_dev_rep_id_lookup
      - name: crm_opp_owner_area_stamped
      - name: crm_opp_owner_business_unit_stamped
      - name: crm_opp_owner_geo_stamped
      - name: crm_opp_owner_region_stamped
      - name: crm_opp_owner_sales_segment_geo_region_area_stamped
      - name: crm_opp_owner_sales_segment_stamped
      - name: crm_opp_owner_stamped_name
      - name: crm_opp_owner_user_role_type_stamped
      - name: crm_sales_dev_rep_id
      - name: days_in_0_pending_acceptance
      - name: days_in_1_discovery
      - name: days_in_2_scoping
      - name: days_in_3_technical_evaluation
      - name: days_in_4_proposal
      - name: days_in_5_negotiating
      - name: days_in_sao
      - name: days_in_stage
      - name: days_since_last_activity
      - name: dbt_scd_id
      - name: dbt_valid_from
      - name: dbt_valid_to
      - name: deal_path
      - name: deal_size
      - name: deployment_preference
      - name: dim_crm_account_id
      - name: dim_crm_opportunity_id
      - name: dim_crm_user_id
      - name: dim_parent_crm_opportunity_id
      - name: distributor
      - name: division_sales_segment_stamped
      - name: downgrade_details
      - name: downgrade_iacv
      - name: downgrade_reason
      - name: dr_deal_id
      - name: dr_partner_deal_type
      - name: dr_partner_engagement
      - name: dr_primary_registration
      - name: dr_status
      - name: duo_net_arr
      - name: duplicate_opportunity_id
      - name: edu_services_value
      - name: enterprise_agile_planning_net_arr
      - name: forecasted_churn_for_clari
      - name: forecasted_iacv
      - name: forecast_category_name
      - name: fpa_master_bookings_flag
      - name: fulfillment_partner
      - name: ga_client_id
      - name: generated_source
      - name: growth_type
      - name: iacv
      - name: iacv_created_date
      - name: incremental_acv
      - name: influence_partner
      - name: intended_product_tier
      - name: investment_services_value
      - name: invoice_number
      - name: iqm_submitted_by_role
      - name: is_closed
      - name: is_closed_deals
      - name: is_comp_new_logo_override
      - name: is_deleted
      - name: is_downgrade
      - name: is_duplicate
      - name: is_edu_oss
      - name: is_focus_partner
      - name: is_lost
      - name: is_pipeline_created_eligible
      - name: is_ps_opp
      - name: is_public_sector_opp
      - name: is_refund
      - name: is_registration_from_portal
      - name: is_renewal
      - name: is_risky
      - name: is_stage_1_plus
      - name: is_stage_3_plus
      - name: is_stage_4_plus
      - name: is_swing_deal
      - name: is_web_portal_purchase
      - name: is_won
      - name: last_activity_date
      - name: last_activity_date_id
      - name: lead_source
      - name: manager_confidence
      - name: merged_crm_opportunity_id
      - name: merged_opportunity_id
      - name: net_arr
      - name: net_arr_stage_1
      - name: net_iacv
      - name: net_incremental_acv
      - name: net_new_source_categories
      - name: new_logo_count
      - name: next_steps
      - name: opportunity_category
      - name: opportunity_deal_size
      - name: opportunity_development_representative
      - name: opportunity_health
      - name: opportunity_id
      - name: opportunity_name
      - name: opportunity_owner
      - name: opportunity_owner_department
      - name: opportunity_owner_manager
      - name: opportunity_term
      - name: opportunity_term_base
      - name: order_type
      - name: order_type_current
      - name: order_type_grouped
      - name: order_type_stamped
      - name: other_non_recurring_amount
      - name: override_arr_basis_clari
      - name: owner_id
      - name: parent_opportunity_id
      - name: parent_segment
      - name: partner_account
      - name: partner_discount
      - name: partner_discount_calc
      - name: partner_initiated_opportunity
      - name: partner_margin_percentage
      - name: partner_track
      - name: payment_schedule
      - name: platform_partner
      - name: primary_campaign_source_id
      - name: primary_solution_architect
      - name: probability
      - name: products_purchased
      - name: product_category
      - name: product_details
      - name: professional_services_value
      - name: proserv_amount
      - name: ptc_predicted_arr
      - name: ptc_predicted_renewal_risk_category
      - name: pushed_count
      - name: qsr_notes
      - name: qsr_status
      - name: raw_net_arr
      - name: reason_for_loss
      - name: reason_for_loss_calc
      - name: reason_for_loss_details
      - name: reason_for_loss_staged
      - name: record_type_id
      - name: recurring_amount
      - name: refund_iacv
      - name: renewal_acv
      - name: renewal_amount
      - name: renewal_forecast_health
      - name: renewal_manager
      - name: renewal_risk_category
      - name: renewal_swing_arr
      - name: resale_partner_track
      - name: risk_reasons
      - name: risk_type
      - name: sales_accepted_date
      - name: sales_accepted_date_id
      - name: sales_last_activity_date
      - name: sales_last_activity_date_id
      - name: sales_path
      - name: sales_qualified_date
      - name: sales_qualified_date_id
      - name: sales_qualified_source
      - name: sales_qualified_source_grouped
      - name: sales_segment
      - name: subscription_type
      - name: sao_crm_opp_owner_area_stamped
      - name: sao_crm_opp_owner_geo_stamped
      - name: sao_crm_opp_owner_region_stamped
      - name: sao_crm_opp_owner_sales_segment_geo_region_area_stamped
      - name: sao_crm_opp_owner_sales_segment_stamped
      - name: sao_crm_opp_owner_sales_segment_stamped_grouped
      - name: sao_crm_opp_owner_segment_region_stamped_grouped
      - name: sa_tech_evaluation_close_status
      - name: sa_tech_evaluation_end_date
      - name: sa_tech_evaluation_start_date
      - name: sdr_or_bdr
      - name: sdr_pipeline_contribution
      - name: solutions_to_be_replaced
      - name: source_buckets
      - name: sqs_bucket_engagement
      - name: ssp_id
      - name: stage_0_pending_acceptance_date
      - name: stage_0_pending_acceptance_date_id
      - name: stage_1_discovery_date
      - name: stage_1_discovery_date_id
      - name: stage_2_scoping_date
      - name: stage_2_scoping_date_id
      - name: stage_3_technical_evaluation_date
      - name: stage_3_technical_evaluation_date_id
      - name: stage_4_proposal_date
      - name: stage_4_proposal_date_id
      - name: stage_5_negotiating_date
      - name: stage_5_negotiating_date_id
      - name: stage_6_awaiting_signature_date
      - name: stage_6_awaiting_signature_date_id
      - name: stage_6_closed_lost_date
      - name: stage_6_closed_lost_date_id
      - name: stage_6_closed_won_date
      - name: stage_6_closed_won_date_id
      - name: stage_name
      - name: stage_name_3plus
      - name: stage_name_4plus
      - name: startup_type
      - name: subscription_end_date
      - name: subscription_end_date_id
      - name: subscription_start_date
      - name: subscription_start_date_id
      - name: tam_notes
      - name: technical_evaluation_date
      - name: technical_evaluation_date_id
      - name: total_contract_value
      - name: true_up_amount
      - name: true_up_value
      - name: upside_swing_deal_iacv
      - name: user_area_stamped
      - name: user_business_unit_stamped
      - name: user_geo_stamped
      - name: user_region_stamped
      - name: user_segment
      - name: user_segment_geo_region_area_stamped
      - name: user_segment_region_stamped_grouped
      - name: user_segment_stamped
      - name: user_segment_stamped_grouped
      - name: valid_deal_count
      - name: vsa_end_date
      - name: vsa_readout
      - name: vsa_start_date
      - name: vsa_start_date_net_arr
      - name: vsa_status
      - name: vsa_url
      - name: won_arr_basis_for_clari
      - name: xdr_net_arr_stage_1
      - name: xdr_net_arr_stage_3
      - name: _last_dbt_run

  - name: prep_crm_opportunity_calcs
    description: '{{ doc("prep_crm_opportunity_calcs") }}'
    data_tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - dim_crm_opportunity_id
            - dbt_valid_from
    columns:
      - name: account_id
      - name: account_owner
      - name: acv
      - name: aggregate_partner
      - name: amount
      - name: arr
      - name: arr_basis
      - name: arr_basis_for_clari
      - name: arr_created_date
      - name: arr_created_date_id
      - name: auto_renewal_status
      - name: calculated_discount
      - name: calculation_days_in_stage_date
      - name: channel_type
      - name: closed_buckets
      - name: close_date
      - name: close_date_id
      - name: close_date_live
      - name: close_fiscal_year
      - name: competitors
      - name: competitors_amazon_code_commit_flag
      - name: competitors_atlassian_flag
      - name: competitors_aws_flag
      - name: competitors_azure_devops_flag
      - name: competitors_azure_flag
      - name: competitors_bamboo_flag
      - name: competitors_bitbucket_flag
      - name: competitors_bitbucket_server_flag
      - name: competitors_circleci_flag
      - name: competitors_github_enterprise_flag
      - name: competitors_github_flag
      - name: competitors_gitlab_core_flag
      - name: competitors_gitlab_flag
      - name: competitors_jenkins_flag
      - name: competitors_none_flag
      - name: competitors_other_flag
      - name: competitors_perforce_flag
      - name: competitors_svn_flag
      - name: competitors_unknown_flag
      - name: competitors_visual_studio_flag
      - name: comp_channel_neutral
      - name: comp_new_logo_override
      - name: contract_reset_opportunity_id
      - name: cp_champion
      - name: cp_close_plan
      - name: cp_decision_criteria
      - name: cp_decision_process
      - name: cp_economic_buyer
      - name: cp_help
      - name: cp_identify_pain
      - name: cp_metrics
      - name: cp_paper_process
      - name: cp_partner
      - name: cp_review_notes
      - name: cp_risks
      - name: cp_score
      - name: cp_use_cases
      - name: cp_value_driver
      - name: cp_why_do_anything_at_all
      - name: cp_why_gitlab
      - name: cp_why_now
      - name: created_date
      - name: created_date_id
      - name: critical_deal_flag
      - name: crm_account_owner_stamped_name
      - name: crm_business_dev_rep_id
      - name: crm_business_dev_rep_id_lookup
      - name: crm_opp_owner_area_stamped
      - name: crm_opp_owner_business_unit_stamped
      - name: crm_opp_owner_geo_stamped
      - name: crm_opp_owner_region_stamped
      - name: crm_opp_owner_sales_segment_geo_region_area_stamped
      - name: crm_opp_owner_sales_segment_stamped
      - name: crm_opp_owner_stamped_name
      - name: crm_opp_owner_user_role_type_stamped
      - name: crm_sales_dev_rep_id
      - name: days_in_0_pending_acceptance
      - name: days_in_1_discovery
      - name: days_in_2_scoping
      - name: days_in_3_technical_evaluation
      - name: days_in_4_proposal
      - name: days_in_5_negotiating
      - name: days_in_sao
      - name: days_in_stage
      - name: days_since_last_activity
      - name: dbt_scd_id
      - name: dbt_updated_at
      - name: dbt_valid_from
      - name: dbt_valid_to
      - name: deal_path
      - name: deal_path_live
      - name: deal_size
      - name: deployment_preference
      - name: dim_crm_account_id
      - name: dim_crm_opportunity_id
      - name: dim_crm_user_id
      - name: dim_parent_crm_opportunity_id
      - name: distributor
      - name: division_sales_segment_stamped
      - name: downgrade_details
      - name: downgrade_iacv
      - name: downgrade_reason
      - name: dr_deal_id
      - name: dr_partner_deal_type
      - name: dr_partner_engagement
      - name: dr_primary_registration
      - name: dr_status
      - name: duo_net_arr
      - name: duplicate_opportunity_id
      - name: edu_services_value
      - name: enterprise_agile_planning_net_arr
      - name: forecasted_churn_for_clari
      - name: forecasted_iacv
      - name: forecast_category_name
      - name: fpa_master_bookings_flag
      - name: fulfillment_partner
      - name: ga_client_id
      - name: generated_source
      - name: growth_type
      - name: iacv
      - name: iacv_created_date
      - name: incremental_acv
      - name: influence_partner
      - name: intended_product_tier
      - name: investment_services_value
      - name: invoice_number
      - name: iqm_submitted_by_role
      - name: is_closed
      - name: is_closed_deals
      - name: is_comp_new_logo_override
      - name: is_deleted
      - name: is_downgrade
      - name: is_duplicate
      - name: is_edu_oss
      - name: is_edu_oss_live
      - name: is_focus_partner
      - name: is_jihu_account_live
      - name: is_lost
      - name: is_pipeline_created_eligible
      - name: is_ps_opp
      - name: is_public_sector_opp
      - name: is_refund
      - name: is_registration_from_portal
      - name: is_renewal
      - name: is_risky
      - name: is_stage_1_plus
      - name: is_stage_3_plus
      - name: is_stage_4_plus
      - name: is_swing_deal
      - name: is_web_portal_purchase
      - name: is_won
      - name: last_activity_date
      - name: last_activity_date_id
      - name: lead_source
      - name: manager_confidence
      - name: merged_crm_opportunity_id
      - name: merged_opportunity_id
      - name: net_arr
      - name: net_arr_stage_1
      - name: net_iacv
      - name: net_incremental_acv
      - name: net_new_source_categories
      - name: new_logo_count
      - name: next_steps
      - name: opportunity_category
      - name: opportunity_category_live
      - name: opportunity_deal_size
      - name: opportunity_development_representative
      - name: opportunity_health
      - name: opportunity_id
      - name: opportunity_name
      - name: opportunity_owner
      - name: opportunity_owner_department
      - name: opportunity_owner_manager
      - name: opportunity_term
      - name: opportunity_term_base
      - name: order_type
      - name: order_type_current
      - name: order_type_grouped
      - name: order_type_grouped_live
      - name: order_type_live
      - name: order_type_stamped
      - name: other_non_recurring_amount
      - name: override_arr_basis_clari
      - name: owner_id
      - name: parent_crm_account_geo_live
      - name: parent_opportunity_id
      - name: parent_segment
      - name: partner_account
      - name: partner_discount
      - name: partner_discount_calc
      - name: partner_initiated_opportunity
      - name: partner_margin_percentage
      - name: partner_track
      - name: payment_schedule
      - name: platform_partner
      - name: primary_campaign_source_id
      - name: primary_key
      - name: primary_solution_architect
      - name: probability
      - name: products_purchased
      - name: product_category
      - name: product_details
      - name: professional_services_value
      - name: proserv_amount
      - name: ptc_predicted_arr
      - name: ptc_predicted_renewal_risk_category
      - name: pushed_count
      - name: qsr_notes
      - name: qsr_status
      - name: raw_net_arr
      - name: reason_for_loss
      - name: reason_for_loss_calc
      - name: reason_for_loss_details
      - name: reason_for_loss_staged
      - name: record_type_id
      - name: recurring_amount
      - name: refund_iacv
      - name: renewal_acv
      - name: renewal_amount
      - name: renewal_forecast_health
      - name: renewal_manager
      - name: renewal_risk_category
      - name: renewal_swing_arr
      - name: resale_partner_track
      - name: risk_reasons
      - name: risk_type
      - name: sales_accepted_date
      - name: sales_accepted_date_id
      - name: sales_last_activity_date
      - name: sales_last_activity_date_id
      - name: sales_path
      - name: sales_qualified_date
      - name: sales_qualified_date_id
      - name: sales_qualified_source
      - name: sales_qualified_source_grouped
      - name: sales_qualified_source_grouped_live
      - name: sales_qualified_source_live
      - name: sales_segment
      - name: sales_segmentation_employees
      - name: subscription_type
      - name: sao_crm_opp_owner_area_stamped
      - name: sao_crm_opp_owner_geo_stamped
      - name: sao_crm_opp_owner_region_stamped
      - name: sao_crm_opp_owner_sales_segment_geo_region_area_stamped
      - name: sao_crm_opp_owner_sales_segment_stamped
      - name: sao_crm_opp_owner_sales_segment_stamped_grouped
      - name: sao_crm_opp_owner_segment_region_stamped_grouped
      - name: sa_tech_evaluation_close_status
      - name: sa_tech_evaluation_end_date
      - name: sa_tech_evaluation_start_date
      - name: sdr_or_bdr
      - name: sdr_pipeline_contribution
      - name: solutions_to_be_replaced
      - name: source_buckets
      - name: sqs_bucket_engagement
      - name: ssp_id
      - name: stage_0_pending_acceptance_date
      - name: stage_0_pending_acceptance_date_id
      - name: stage_1_discovery_date
      - name: stage_1_discovery_date_id
      - name: stage_2_scoping_date
      - name: stage_2_scoping_date_id
      - name: stage_3_technical_evaluation_date
      - name: stage_3_technical_evaluation_date_id
      - name: stage_4_proposal_date
      - name: stage_4_proposal_date_id
      - name: stage_5_negotiating_date
      - name: stage_5_negotiating_date_id
      - name: stage_6_awaiting_signature_date
      - name: stage_6_awaiting_signature_date_id
      - name: stage_6_closed_lost_date
      - name: stage_6_closed_lost_date_id
      - name: stage_6_closed_won_date
      - name: stage_6_closed_won_date_id
      - name: stage_name
      - name: stage_name_3plus
      - name: stage_name_4plus
      - name: startup_type
      - name: subscription_end_date
      - name: subscription_end_date_id
      - name: subscription_start_date
      - name: subscription_start_date_id
      - name: tam_notes
      - name: technical_evaluation_date
      - name: technical_evaluation_date_id
      - name: total_contract_value
      - name: true_up_amount
      - name: true_up_value
      - name: ultimate_parent_sales_segment_emp
      - name: upside_swing_deal_iacv
      - name: user_area_stamped
      - name: user_business_unit_stamped
      - name: user_geo_stamped
      - name: user_region_stamped
      - name: user_segment
      - name: user_segment_geo_region_area_stamped
      - name: user_segment_region_stamped_grouped
      - name: user_segment_stamped
      - name: user_segment_stamped_grouped
      - name: valid_deal_count
      - name: vsa_end_date
      - name: vsa_readout
      - name: vsa_start_date
      - name: vsa_start_date_net_arr
      - name: vsa_status
      - name: vsa_url
      - name: won_arr_basis_for_clari
      - name: xdr_net_arr_stage_1
      - name: xdr_net_arr_stage_3
      - name: _last_dbt_run

  - name: prep_crm_opportunity_daily
    description: '{{ doc("prep_crm_opportunity_daily") }}'
    columns:
      - name: crm_opportunity_snapshot_id
        data_tests:
          - not_null
          - unique
      - name: snapshot_date
      - name: account_id
      - name: account_owner
      - name: acv
      - name: aggregate_partner
      - name: amount
      - name: arr
      - name: arr_basis
      - name: arr_basis_for_clari
      - name: arr_created_date
      - name: arr_created_date_id
      - name: auto_renewal_status
      - name: calculated_discount
      - name: calculation_days_in_stage_date
      - name: channel_type
      - name: closed_buckets
      - name: close_date
      - name: close_date_id
      - name: close_fiscal_year
      - name: competitors
      - name: competitors_amazon_code_commit_flag
      - name: competitors_atlassian_flag
      - name: competitors_aws_flag
      - name: competitors_azure_devops_flag
      - name: competitors_azure_flag
      - name: competitors_bamboo_flag
      - name: competitors_bitbucket_flag
      - name: competitors_bitbucket_server_flag
      - name: competitors_circleci_flag
      - name: competitors_github_enterprise_flag
      - name: competitors_github_flag
      - name: competitors_gitlab_core_flag
      - name: competitors_gitlab_flag
      - name: competitors_jenkins_flag
      - name: competitors_none_flag
      - name: competitors_other_flag
      - name: competitors_perforce_flag
      - name: competitors_svn_flag
      - name: competitors_unknown_flag
      - name: competitors_visual_studio_flag
      - name: comp_channel_neutral
      - name: comp_new_logo_override
      - name: contract_reset_opportunity_id
      - name: cp_champion
      - name: cp_close_plan
      - name: cp_decision_criteria
      - name: cp_decision_process
      - name: cp_economic_buyer
      - name: cp_help
      - name: cp_identify_pain
      - name: cp_metrics
      - name: cp_paper_process
      - name: cp_partner
      - name: cp_review_notes
      - name: cp_risks
      - name: cp_score
      - name: cp_use_cases
      - name: cp_value_driver
      - name: cp_why_do_anything_at_all
      - name: cp_why_gitlab
      - name: cp_why_now
      - name: created_date
      - name: created_date_id
      - name: critical_deal_flag
      - name: crm_account_owner_stamped_name
      - name: crm_business_dev_rep_id
      - name: crm_business_dev_rep_id_lookup
      - name: crm_opp_owner_area_stamped
      - name: crm_opp_owner_business_unit_stamped
      - name: crm_opp_owner_geo_stamped
      - name: crm_opp_owner_region_stamped
      - name: crm_opp_owner_sales_segment_geo_region_area_stamped
      - name: crm_opp_owner_sales_segment_stamped
      - name: crm_opp_owner_stamped_name
      - name: crm_opp_owner_user_role_type_stamped
      - name: crm_sales_dev_rep_id
      - name: days_in_0_pending_acceptance
      - name: days_in_1_discovery
      - name: days_in_2_scoping
      - name: days_in_3_technical_evaluation
      - name: days_in_4_proposal
      - name: days_in_5_negotiating
      - name: days_in_sao
      - name: days_in_stage
      - name: days_since_last_activity
      - name: dbt_scd_id
      - name: dbt_valid_from
      - name: dbt_valid_to
      - name: deal_path
      - name: deal_size
      - name: deployment_preference
      - name: dim_crm_account_id
      - name: dim_crm_opportunity_id
      - name: dim_crm_user_id
      - name: dim_parent_crm_opportunity_id
      - name: distributor
      - name: division_sales_segment_stamped
      - name: downgrade_details
      - name: downgrade_iacv
      - name: downgrade_reason
      - name: dr_deal_id
      - name: dr_partner_deal_type
      - name: dr_partner_engagement
      - name: dr_primary_registration
      - name: dr_status
      - name: duo_net_arr
      - name: duplicate_opportunity_id
      - name: edu_services_value
      - name: enterprise_agile_planning_net_arr
      - name: forecasted_churn_for_clari
      - name: forecasted_iacv
      - name: forecast_category_name
      - name: fpa_master_bookings_flag
      - name: fulfillment_partner
      - name: ga_client_id
      - name: generated_source
      - name: growth_type
      - name: iacv
      - name: iacv_created_date
      - name: incremental_acv
      - name: influence_partner
      - name: intended_product_tier
      - name: investment_services_value
      - name: invoice_number
      - name: iqm_submitted_by_role
      - name: is_closed
      - name: is_closed_deals
      - name: is_comp_new_logo_override
      - name: is_deleted
      - name: is_downgrade
      - name: is_duplicate
      - name: is_edu_oss
      - name: is_focus_partner
      - name: is_lost
      - name: is_pipeline_created_eligible
      - name: is_ps_opp
      - name: is_public_sector_opp
      - name: is_refund
      - name: is_registration_from_portal
      - name: is_renewal
      - name: is_risky
      - name: is_stage_1_plus
      - name: is_stage_3_plus
      - name: is_stage_4_plus
      - name: is_swing_deal
      - name: is_web_portal_purchase
      - name: is_won
      - name: last_activity_date
      - name: last_activity_date_id
      - name: lead_source
      - name: manager_confidence
      - name: merged_crm_opportunity_id
      - name: merged_opportunity_id
      - name: net_arr
      - name: net_arr_stage_1
      - name: net_iacv
      - name: net_incremental_acv
      - name: net_new_source_categories
      - name: new_logo_count
      - name: next_steps
      - name: opportunity_category
      - name: opportunity_deal_size
      - name: opportunity_development_representative
      - name: opportunity_health
      - name: opportunity_id
      - name: opportunity_name
      - name: opportunity_owner
      - name: opportunity_owner_department
      - name: opportunity_owner_manager
      - name: opportunity_term
      - name: opportunity_term_base
      - name: order_type
      - name: order_type_current
      - name: order_type_grouped
      - name: order_type_stamped
      - name: other_non_recurring_amount
      - name: override_arr_basis_clari
      - name: owner_id
      - name: parent_opportunity_id
      - name: parent_segment
      - name: partner_account
      - name: partner_discount
      - name: partner_discount_calc
      - name: partner_initiated_opportunity
      - name: partner_margin_percentage
      - name: partner_track
      - name: payment_schedule
      - name: platform_partner
      - name: primary_campaign_source_id
      - name: primary_solution_architect
      - name: probability
      - name: products_purchased
      - name: product_category
      - name: product_details
      - name: professional_services_value
      - name: proserv_amount
      - name: ptc_predicted_arr
      - name: ptc_predicted_renewal_risk_category
      - name: pushed_count
      - name: qsr_notes
      - name: qsr_status
      - name: raw_net_arr
      - name: reason_for_loss
      - name: reason_for_loss_calc
      - name: reason_for_loss_details
      - name: reason_for_loss_staged
      - name: record_type_id
      - name: recurring_amount
      - name: refund_iacv
      - name: renewal_acv
      - name: renewal_amount
      - name: renewal_forecast_health
      - name: renewal_manager
      - name: renewal_risk_category
      - name: renewal_swing_arr
      - name: resale_partner_track
      - name: risk_reasons
      - name: risk_type
      - name: sales_accepted_date
      - name: sales_accepted_date_id
      - name: sales_last_activity_date
      - name: sales_last_activity_date_id
      - name: sales_path
      - name: sales_qualified_date
      - name: sales_qualified_date_id
      - name: sales_qualified_source
      - name: sales_qualified_source_grouped
      - name: sales_segment
      - name: subscription_type
      - name: sao_crm_opp_owner_area_stamped
      - name: sao_crm_opp_owner_geo_stamped
      - name: sao_crm_opp_owner_region_stamped
      - name: sao_crm_opp_owner_sales_segment_geo_region_area_stamped
      - name: sao_crm_opp_owner_sales_segment_stamped
      - name: sao_crm_opp_owner_sales_segment_stamped_grouped
      - name: sao_crm_opp_owner_segment_region_stamped_grouped
      - name: sa_tech_evaluation_close_status
      - name: sa_tech_evaluation_end_date
      - name: sa_tech_evaluation_start_date
      - name: sdr_or_bdr
      - name: sdr_pipeline_contribution
      - name: solutions_to_be_replaced
      - name: source_buckets
      - name: sqs_bucket_engagement
      - name: ssp_id
      - name: stage_0_pending_acceptance_date
      - name: stage_0_pending_acceptance_date_id
      - name: stage_1_discovery_date
      - name: stage_1_discovery_date_id
      - name: stage_2_scoping_date
      - name: stage_2_scoping_date_id
      - name: stage_3_technical_evaluation_date
      - name: stage_3_technical_evaluation_date_id
      - name: stage_4_proposal_date
      - name: stage_4_proposal_date_id
      - name: stage_5_negotiating_date
      - name: stage_5_negotiating_date_id
      - name: stage_6_awaiting_signature_date
      - name: stage_6_awaiting_signature_date_id
      - name: stage_6_closed_lost_date
      - name: stage_6_closed_lost_date_id
      - name: stage_6_closed_won_date
      - name: stage_6_closed_won_date_id
      - name: stage_name
      - name: stage_name_3plus
      - name: stage_name_4plus
      - name: startup_type
      - name: subscription_end_date
      - name: subscription_end_date_id
      - name: subscription_start_date
      - name: subscription_start_date_id
      - name: tam_notes
      - name: technical_evaluation_date
      - name: technical_evaluation_date_id
      - name: total_contract_value
      - name: true_up_amount
      - name: true_up_value
      - name: upside_swing_deal_iacv
      - name: user_area_stamped
      - name: user_business_unit_stamped
      - name: user_geo_stamped
      - name: user_region_stamped
      - name: user_segment
      - name: user_segment_geo_region_area_stamped
      - name: user_segment_region_stamped_grouped
      - name: user_segment_stamped
      - name: user_segment_stamped_grouped
      - name: valid_deal_count
      - name: vsa_end_date
      - name: vsa_readout
      - name: vsa_start_date
      - name: vsa_start_date_net_arr
      - name: vsa_status
      - name: vsa_url
      - name: won_arr_basis_for_clari
      - name: xdr_net_arr_stage_1
      - name: xdr_net_arr_stage_3
      - name: _last_dbt_run

  - name: prep_marketo_activity
    description: The core prep table of Marketo's activity data. Note - the `dim_marketing_contact` CTE is built off of dim and bdg tables, intentionally, due to the field logic present in those tables that is needed in this prep model. 
    columns:
      - name: marketo_activity_sk
        description: The surrogate key for each marketo person's activity.
        data_tests:
          - not_null
          - unique
      - name: dim_marketo_person_id
        description: The Unique person ID from Marketo
        data_tests:
          - not_null
      - name: dim_marketo_activity_id
        description: The activity's ID from Marketo
        data_tests:
          - not_null
      - name: activity_datetime
        description: The datetime of the activity
        data_tests:
          - not_null
      - name: activity_date
        description: The date of the activity
        data_tests:
          - not_null
      - name: campaign_name
        description: The campaign name, outside of Marketo
      - name: campaign_id
        description: The campaign ID, ouside of Marketo
      - name: marketo_campaign_id
        description: The Marketo campaign id
      - name: campaign_type
        description: The campaign's type
      - name: campaign_member_status
        description: The campaign member's status
      - name: marketo_form_name
        description: The Marketo Form's name
      - name: marketo_form_id
        description: The Marketo Form's ID
      - name: form_fields
        description: The Marketo form fields on the form
      - name: marketo_webpage_id
        description: The Marketo Webpage's id
      - name: marketo_query_parameters
        description: The query parameters
      - name: marketo_referrer_url
        description: The referrer URL
      - name: marketo_form_client_ip_address_hash
        description: The client's IP as a hash
      - name: linkedin_form_name
        description: The LinkedIn Form's name
      - name: webpage
        description: The webpage of the activity
      - name: webpage_url
        description: The webpage URL of the activity
      - name: search_engine
        description: The search engine used by the person
      - name: search_query
        description: The query searched by the person
      - name: activity_type_id
        description: The ID of the marketo activity type
      - name: marketo_nurture
        description: The Marketo Nurture's name
      - name: marketo_nurture_id
        description: The Marketo Nurture's ID
      - name: marketo_nurture_previous_track_id
        description: The Marketo Nurture's previous track ID
      - name: marketo_nurture_new_track_id
        description: The Marketo Nurture's new track ID
      - name: marketo_email_name
        description: The name of the Marketo email
      - name: marketo_email_id
        description: The ID of the Marketo email
      - name: marketo_campaign_run_id
        description: The run ID of the Marketo campaign
      - name: marketo_email_category
        description: The category of the Marketo email
      - name: marketo_email_bounce_reason
        description: The bounce reason
      - name: marketo_email_subcategory
        description: The subcategory of the Marketo email
      - name: marketo_email_program_step_id
        description: The Marketo PRogram's step ID
      - name: marketo_email_test_variant
        description: The test variant of the Marketo email
      - name: changed_score_field
        description: Which score field was changed
      - name: scoring_rule
        description: The scoring rule invoked for the change
      - name: score_change_original_value
        description: The lead score before change
      - name: score_change_new_value
        description: The lead score after change
      - name: score_change_net_value
        description: The delta between old and new scores for score change activities
      - name: clicked_url
        description: The clicked URL
      - name: push_lead_to_marketo_source
        description: How the source from which the lead wa spushed to MArketo
      - name: push_lead_to_marketo_source_id
        description: The ID of the push_lead_to_marketo_source
      - name: marketo_score_decay_reason
        description: The reason for the score decay
      - name: marketo_score_decay_type
        description: The type of reason for the score decay
      - name: trial_type
        description: The type of trial
      - name: activity_type
        description: The type of activity
      - name: in_current_scoring_model
        description: Whether the activity is in the current scoring model
      - name: scored_action
        description: The action being scored
      - name: is_person_activity
        description: T/F flag that indicates if the activity was person-driven or not

  - name: prep_quote
    description: '{{ doc("prep_quote") }}'
    columns:
      - name: dim_quote_id
        description: The unique identifier for each of the quotes.
        data_tests:
          - not_null
          - unique
      - name: quote_number
      - name: quote_name
      - name: quote_status
      - name: is_primary_quote
      - name: quote_start_date
      - name: subscription_action_type
        description: '{{ doc("subscription_action_type") }}'
      - name: is_education_exemption_comment
        description: Boolean field indicating whether or not the quote is exempt from the EDU perspective.
      - name: is_education_approval_required
        description: Boolean field indicating whether or not the quote needs approvals from the EDU perspective. 

  - name: prep_recurring_charge_subscription_monthly
    description: '{{ doc("prep_recurring_charge_subscription_monthly") }}'
    columns:
      - name: dim_subscription_id
        description: Unique identifier of a version of a subscription
        data_tests:
          - not_null
        tags: ["tdf", "common", "gainsight"]
      - name: dim_subscription_id_original
        description: Identifier of the original subscription in lineage of a given Subscription ID
        data_tests:
          - not_null
        tags: ["tdf", "common", "gainsight"]
      - name: dim_billing_account_id
        description: ID of the Zuora account associated with a given Subscription ID
        data_tests:
          - not_null
      - name: dim_crm_account_id
        description: Account ID from SFDC identifing the customer
      - name: dim_date_id
        description: The identifier of date month in dim_date
        data_tests:
          - not_null
      - name: subscription_status
        description: Status of the subscription
        data_tests:
          - not_null
          - accepted_values:
              values: ['Active', 'Cancelled', 'Draft', 'Expired']
        tags: ["tdf", "common", "gainsight"]
      - name: unit_of_measure
      - name: total_mrr
        description: Monthly Recurring Revenue value for given month for a given Subscription ID
      - name: total_arr
        description: Annual Recurring Revenue value for given month for a given Subscription ID
      - name: total_quantity
        description: Total quantity
      - name: sm_mrr
        description: Monthly Recurring Revenue value for given month from Self-Managed products
      - name: sm_arr
        description: Annual Recurring Revenue value for given month from Self-Managed products
      - name: sm_quantity
        description: Total quantity of Self-Managed products
      - name: saas_mrr
        description: Monthly Recurring Revenue value for given month from SaaS products
      - name: saas_arr
        description: Annual Recurring Revenue value for given month from SaaS products
      - name: saas_quantity
        description: Total quantity of SaaS products
      - name: other_mrr
        description: Monthly Recurring Revenue value for given month from products that are neither Self-Managed nor SaaS
      - name: other_arr
        description: Annual Recurring Revenue value for given month from products that are neither Self-Managed nor SaaS
      - name: other_quantity
        description: Total quantity of products that are neither Self-Managed nor SaaS
      - name: is_latest_record_per_subscription
        description: Flags if the row contains the most recent record received for the subscription.

  - name: prep_mart_arr_snapshot_base
    description: Base model for the mart_arr snapshot data
    columns:
      - name: primary_key
        data_tests:
          - not_null
      - name: dbt_valid_from
        data_tests:
          - not_null

  - name: prep_mart_available_to_renew_snapshot_base
    description: Base model for the mart_available_to_renew snapshot data
    columns:
      - name: primary_key
        data_tests:
          - not_null
      - name: dbt_valid_from
        data_tests:
          - not_null

  - name: prep_sfdc_account
    description: '{{ doc("prep_sfdc_account") }}'
    columns:
      - name: dim_account_industry_name_source
        description: Dim industry name final source column
      - name: dim_account_location_country_name_source
        description: Dim location country name final source column
      - name: dim_crm_account_id
        description: CRM Account ID provided by Salesforce
        data_tests:
          - not_null
      - name: dim_parent_crm_account_id
        description: Ulimate parent CRM Account ID provided by Salesforce
      - name: dim_parent_industry_name_source
        description: Dim industry name final source column
      - name: dim_parent_sales_segment_grouped_source
      - name: dim_parent_sales_segment_name_source
        description: Dim sales segment name final source column
      - name: dim_parent_sales_territory_name_source
        description: Dim sales territory name final source column

  - name: prep_subscription_opportunity_mapping
    description: '{{ doc("prep_subscription_opportunity_mapping") }}'
    columns:
      - name: dim_subscription_id
        data_tests:
          - not_null
      - name: dim_billing_account_id
        description: Unique identifier of a Zuora Billing account
      - name: subscription_name
        description: Unique name of a subscription
      - name: subscription_sales_type
        description: Self-Service or Sales-Assisted, as defined by the Zuora user who created the subscription
      - name: subscription_account_id
        description: Unique identifier of a SFDC account, as associated with the dim_subscription_id
      - name: subscription_parent_account_id
        description: Unique identifier of an ultimate parent SFDC account, as associated with the dim_subscription_id
      - name: invoice_opp_account_id_forward
        description: Unique identifier of a SFDC account, as associated with the opportunity in the subscription's invoice.
      - name: invoice_opp_account_id_backward
        description: Unique identifier of a SFDC account, as associated with the opportunity in the subscription's invoice, as filled in backwards based on the various filling rules.
      - name: quote_opp_account_id_forward
        description: Unique identifier of a SFDC account, as associated with the opportunity in the subscription's invoice, as filled in forwards based on the various filling rules.
      - name: quote_opp_account_id_backward
        description: Unique identifier of a SFDC account, as associated with the opportunity in the subscription's quote.
      - name: subscription_opp_name_opp_account_id_forward
        description: Unique identifier of a SFDC account, as associated with the opportunity in the subscription's quote, as filled in forwards based on the various filling rules.
      - name: subscription_opp_name_opp_account_id_backward
        description: Unique identifier of a SFDC account, as associated with the opportunity in the subscription's quote, as filled in backwards based on the various filling rules.
      - name: subscription_version
        description: Version of a subscription
      - name: term_start_date
        description: Subscription's term start date
      - name: term_end_date
        description: Subscription's term end date
      - name: subscription_start_date
        description: Date the subscription begins
      - name: subscription_end_date
        description: Date the subscription ends
      - name: subscription_status
        description: Subscription status (active, expired, etc.)
      - name: subscription_created_date
        description: Date the subscription was created
      - name: subscription_source_opp_id
        description: Opportunity_id found on the subscription object
      - name: subscription_opp_id
        description: Opportunity_id for self-service subscriptions only since this is considered to have high fidelity
      - name: invoice_opp_id_forward
        description: Unique identifier of a opportunity from subscription's invoice, as filled in forwards based on the the subscription's created_date being shared with another version of the subscription.
      - name: invoice_opp_id_backward
        description: Unique identifier of a opportunity from subscription's invoice, as filled in backwards based on the the subscription's created_date being shared with another version of the subscription.
      - name: invoice_opp_id_forward_term_based
        description: Unique identifier of a opportunity from subscription's invoice, as filled in forwards based on the the subscription's term dates being shared with another version of the subscription.
      - name: invoice_opp_id_backward_term_based
        description: Unique identifier of a opportunity from subscription's invoice, as filled in backwards based on the the subscription's term dates being shared with another version of the subscription.
      - name: invoice_opp_id_forward_sub_name
        description: Unique identifier of a opportunity from subscription's invoice, as filled in forwards based on the the subscription's name being shared with another version of the subscription.
      - name: unfilled_invoice_opp_id
        description: Unique identifier of a opportunity from subscription's invoice.
      - name: quote_opp_id_forward
        description: Unique identifier of a opportunity from subscription's quote, as filled in forwards based on the the subscription's created_date being shared with another version of the subscription.
      - name: quote_opp_id_backward
        description: Unique identifier of a opportunity from subscription's quote, as filled in backwards based on the the subscription's created_date being shared with another version of the subscription.
      - name: quote_opp_id_forward_term_based
        description: Unique identifier of a opportunity from subscription's quote, as filled in forwards based on the the subscription's term dates being shared with another version of the subscription.
      - name: quote_opp_id_backward_term_based
        description: Unique identifier of a opportunity from subscription's quote, as filled in backwards based on the the subscription's term dates being shared with another version of the subscription.
      - name: quote_opp_id_forward_sub_name
        description: Unique identifier of a opportunity from subscription's quote, as filled in forwards based on the the subscription's name being shared with another version of the subscription.
      - name: unfilled_quote_opp_id
        description: Unique identifier of a opportunity from subscription's quote.
      - name: subscription_quote_number_opp_id_forward
        description: Unique identifier of a opportunity from subscription's quote_number on the subscription object, as filled in forwards based on the the subscription's created_date being shared with another version of the subscription.
      - name: subscription_quote_number_opp_id_backward
        description: Unique identifier of a opportunity from subscription's quote_number on the subscription object, as filled in backwards based on the the subscription's created_date being shared with another version of the subscription.
      - name: subscription_quote_number_opp_id_forward_term_based
        description: Unique identifier of a opportunity from subscription's quote_number on the subscription object, as filled in forwards based on the the subscription's term dates being shared with another version of the subscription.
      - name: subscription_quote_number_opp_id_backward_term_based
        description: Unique identifier of a opportunity from subscription's quote_number on the subscription object, as filled in backwards based on the the subscription's term dates being shared with another version of the subscription.
      - name: subscription_quote_number_opp_id_forward_sub_name
        description: Unique identifier of a opportunity from subscription's quote_number on the subscription object, as filled in forwards based on the the subscription's name being shared with another version of the subscription.
      - name: unfilled_subscription_quote_number_opp_id
        description: Unique identifier of a opportunity from subscription's quote_number on the subscription object.
      - name: combined_opportunity_id
        description: Taking all of the potential opportunity ids, choose the best fit based on rules approved by the Data Team and Enterprise Apps.

  - name: prep_mart_retention_parent_account_snapshot_base
    description: Base model for the fct_retention snapshot data
    columns:
      - name: fct_retention_id
        data_tests:
          - not_null
      - name: dbt_valid_from
        data_tests:
          - not_null

  - name: prep_fct_mrr_snapshot_base
    description: Base model for the fct_mrr snapshot data
    columns:
      - name: mrr_id
        data_tests:
          - not_null
      - name: dbt_valid_from
        data_tests:
          - not_null

  - name: prep_order_action_rate_plan
    description: Prep table for Zuora order action. This it at the order action grain i.e. one order can have multiple order actions.
    columns:
      - name: dim_order_action_id
        description: Unique identifier of a version of an order action
      - name: dim_order_id
        description: Unique identifier for each order which groups all order actions within an order
      - name: dim_subscription_id
      - name: dim_amendment_id
      - name: order_number
        description: Order number from Zuora. Not unique in this model as one order can have multiple order actions
      - name: rate_plan_id
        description: ID of the Zuora rate plan
      - name: rate_plan_name
        description: Name of the Zuora rate plan
      - name: product_rate_plan_id
        description: The id of the rate plan coming from the Zuora product catalog
      - name: amendement_type
        description: Shows the type of the most recent amendment that touched a particular RatePlan.
      - name: order_action_type
        description: The type of order action which took place. For example; 'UpdateProduct', 'RenewSubscription'
      - name: order_action_sequence
        description: The sequence in which the actions were carried out on an order.
      - name: order_action_created_date
        description: The date the order action was created
      - name: rate_plan_created_date
        description: The date the rate plan was created
  
  - name: prep_sales_funnel_target
    description: Prep model for SSOT sales funnel targets
    columns:
      - name: dim_crm_user_hierarchy_sk
        data_tests:
          - not_null

  - name: prep_renewal_fiscal_years
    description: This model is used in mart_available_to_renew to calculate which years are to be processed.

  - name: prep_lead
    description: '{{ doc("prep_lead") }}'
    columns:
      - name: dim_lead_sk
        description: The unique surrogate key of the model that is created by hashing `leads_id`. It cannot be used as a join key
        data_tests:
          - unique
          - not_null
      - name: internal_lead_id
        description: The unique id of a lead. It is system-generated and is specific for CDot only and cannot be joined to leads from other source systems
        data_tests:
          - unique
          - not_null
      - name: dim_namespace_id
        description: Gitlab.com namespace id where the lead originates
      - name: user_id
        description: The user id which belongs to the lead
      - name: first_name_hash
        description: The first name of the lead. It is hashed for secuirty compliance reasons
      - name: last_name_hash
        description: The last name of the lead. It is hashed for security compliance reasons
      - name: email_hash
        description: The email address of the lead. It is hashed for security compliance reasons
      - name: phone_hash
        description: The phone number of the lead. It is hashed for security compliance reasons
      - name: created_at
        description: The date when the lead was created
      - name: updated_at
        description: The date when the lead was updated
      - name: trial_start_date
        description: The date when trial starts for trial leads
      - name: opt_in
        description: Default true for communication
      - name: currently_in_trial
        description: Whether the lead is currently in trial in Marketo
      - name: is_for_business_use
        description: Whether the trial is for business purposes
      - name: employees_bucket
        description: Company size with predefined buckets
      - name: country
        description: The country as entered by the trial user during registration
      - name: state
        description: The state as entered by the trial user during registration
      - name: product_interaction
        description: The type of lead; hand raise or trial
      - name: provider
        description: Gitlab for the time being
      - name: comment_capture
        description: A message to the sales team
      - name: glm_content
        description: Part of the app lead comes from
      - name: glm_source
        description: Domain the lead comes from
      - name: sent_at
        description: The time when the lead is sent to Workato
      - name: website_url
        description: Website url of the company
      - name: role
        description: Role at the company
      - name: jobs_to_be_done
        description: Jobs to be done, or what is to be accomplished
      
  - name: prep_yearlies_target
    description: Unpivoted version of yearlies_target to join with yearlies_actual
    columns:
      - name: yearly_name
        description: name of the yearly item
        data_tests:
          - not_null
      - name: yearly_dri
        description: DRI of the yearly item
      - name: yearly_description
        description: description of the yearly item
      - name: targets_raw
        description: raw targets of the yearly item (includes mnpi)
      - name: quarter
        description: quarter of the yearly target
      - name: is_mnpi
        description: boolean flag whether the yearly item is mnpi or not

  - name: prep_sales_dev_user_hierarchy
    description: View of the Sales Dev User Hierarchy
    data_tests:
    - dbt_utils.unique_combination_of_columns:
        combination_of_columns:
          - dim_crm_user_id
          - snapshot_date
    columns: 
      - name: dim_crm_user_id
        description: The SFDC User id
        data_tests:
          - not_null
      - name: sales_dev_rep_employee_number
        description: The Sales Dev Rep's employee number
      - name: sales_dev_rep_role_name
        description: The Sales Dev Rep's Role Name from SFDC
      - name: sales_dev_rep_user_full_name
        description: The Sales Dev Rep's Full Name
      - name: sales_dev_manager_employee_number
        description: The Sales Dev Rep's Manager's employee number
      - name: sales_dev_rep_manager_full_name
        description: The Sales Dev Rep's Manager's role name from SFDC.
      - name: sales_dev_manager_full_name
        description: The Sales Dev Rep's Manager's full name. 
      - name: sales_dev_leader_employee_number
        description: The Sales Dev Rep's Leader's employee number
      - name: sales_dev_leader_user_role_name
        description: The Sales Dev Rep's Leader's role name from SFDC.
      - name: sales_dev_rep_leader_full_name
        description: The Sales Dev Rep's Leader's full name. 
      - name: snapshot_date
        description: The date the snapshot was last updated for the hierarchy of that record. 
      - name: sales_dev_rep_user_role_level_1
        description: The Sales Dev Rep's role hierarchy level 1
      - name: sales_dev_rep_user_role_level_2
        description: The Sales Dev Rep's role hierarchy level 2
      - name: sales_dev_rep_user_role_level_3
        description: The Sales Dev Rep's role hierarchy level 3
      - name: sales_dev_rep_user_role_hierarchy_fiscal_year
        description: The fiscal year of the snapshotted Sales Dev Rep role hierarchy being used. 
  - name: prep_zendesk_users_masked_source  
    description: This model combines Zendesk user data with organization information to provide a comprehensive view of Zendesk users and their associated organizations.    
    columns:
      - name: user_id
        description: Unique identifier for the Zendesk user
        tests:
          - not_null
          - unique
      - name: organization_id
      - name: name
        description: Full name of the Zendesk user# 
#         meta:
#            masking_policy: sensitive_pii_data_viewer
      - name: email
        description: Email address of the Zendesk user# 
#         meta:
#            masking_policy: sensitive_pii_data_viewer
      - name: phone
        description: Phone number of the Zendesk user# 
#         meta:
#            masking_policy: sensitive_pii_data_viewer
      - name: is_restricted_agent
        description: Boolean flag indicating if the user is a restricted agent (TRUE if the agent has restrictions on access)
      - name: role
        description: Role of the user in Zendesk (e.g., admin, agent, end-user)
      - name: is_suspended
        description: Boolean flag indicating if the user account is suspended (TRUE if the user is suspended)
      - name: time_zone
        description: Time zone setting of the Zendesk user
      - name: user_region
        description: Region where the user is located# 
#         meta:
#            masking_policy: sensitive_pii_data_viewer
      - name: tags
        description: Array of tags associated with the Zendesk user, NULL if no tags are present
      - name: sfdc_account_id
        description: Salesforce Account ID associated with the organization
      - name: organization_name
        description: Name of the organization the user belongs to
      - name: created_at
        description: Timestamp when the Zendesk user was created
        tests:
          - not_null
      - name: updated_at
        description: Timestamp when the Zendesk user was last updated
        tests:
          - not_null
