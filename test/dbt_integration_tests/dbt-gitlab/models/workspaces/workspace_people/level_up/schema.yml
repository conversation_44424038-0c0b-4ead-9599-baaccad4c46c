version: 2

models:
- name: wk_level_up_course_actions
  description: Returns information when a user enrolls, changes status, earns a certificate, or has their access revoked in a course.
- name: wk_level_up_course_completions
  description: Returns information when a course is completed by a user 
- name: wk_level_up_course_views
  description: Returns information about pages within courses that are viewed as a learner
- name: wk_level_up_logins
  description: Returns information about logins. It includes browser & IP Address information
- name: wk_level_up_visits
  description: Returns information about logged-in users visiting the platform
