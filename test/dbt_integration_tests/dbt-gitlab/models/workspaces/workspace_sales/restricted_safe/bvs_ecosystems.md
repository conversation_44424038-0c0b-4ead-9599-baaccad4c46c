{% docs bvs_ecosystems_document %}

Search across documents for filtered data. More details in the [document](https://www.ecosystems.us/clients/eco/EcoPublicAPIs.pdf).

{% enddocs %}

{% docs bvs_ecosystems_account_name %}

Account name.

{% enddocs %}

{% docs bvs_ecosystems_collaborators %}

List of collaborators in json file format.

{% enddocs %}

{% docs bvs_ecosystems_crm_account_id %}

CRM account identification.

{% enddocs %}

{% docs bvs_ecosystems_crm_account_name %}

CRM account name.

{% enddocs %}

{% docs bvs_ecosystems_crm_account_owner_email %}

CRM account owner email.

{% enddocs %}

{% docs bvs_ecosystems_crm_account_phone %}

CRM account phone number.

{% enddocs %}

{% docs bvs_ecosystems_crm_opportunity_id %}

CRM opportunity identification.

{% enddocs %}

{% docs bvs_ecosystems_crm_opportunity_name %}

CRM opportunity name.

{% enddocs %}

{% docs bvs_ecosystems_crm_opportunity_owner_email %}

CRM opportunity owner email.

{% enddocs %}

{% docs bvs_ecosystems_crm_opportunity_phone %}

CRM opportunity phone.

{% enddocs %}

{% docs bvs_ecosystems_is_demo %}

Is demo record.

{% enddocs %}

{% docs bvs_ecosystems_document_name %}

Document name.

{% enddocs %}

{% docs bvs_ecosystems_document_status %}

Document status

{% enddocs %}

{% docs bvs_ecosystems_document_id %}

Document id.

{% enddocs %}

{% docs bvs_ecosystems_update_history_created_at %}

Update history created at date.

{% enddocs %}

{% docs bvs_ecosystems_update_history_created_by_id %}

Update history created by id.

{% enddocs %}

{% docs bvs_ecosystems_update_history_created_by_username %}

Update history created by username.

{% enddocs %}

{% docs bvs_ecosystems_update_history_last_updated_at %}

Update history last updated at.

{% enddocs %}

{% docs bvs_ecosystems_update_history_updated_by_id %}

Update history last updated by id.

{% enddocs %}

{% docs bvs_ecosystems_update_history_updated_by_username %}

Update history last updated by username.

{% enddocs %}

{% docs bvs_ecosystems_uploaded_at %}

Timestamp when the record is extracted.

{% enddocs %}


{% docs bvs_ecosystems_cva %}

A CSV report providing ViViEN Analytics for every CVA document, similar to ViViEN insights portal within the Ecosystems platform.
More details in the [document](https://www.ecosystems.us/clients/eco/EcoPublicAPIs.pdf).

Documentation [Collaborative Value Assessment](https://www.ecosystems.io/collaborative-value-assessment).

{% enddocs %}


{% docs bvs_ecosystems_active_external_collaborator %}

External collaborator.

{% enddocs %}

{% docs bvs_ecosystems_active_internal_collaborator %}

Internal collaborator.

{% enddocs %}

{% docs bvs_ecosystems_active_partner_collaborator %}

Active partner collaborator.

{% enddocs %}

{% docs bvs_ecosystems_count_of_value_drivers_modified %}

Count of VDS modified.

{% enddocs %}

{% docs bvs_ecosystems_is_discovery_modified %}

Discovery modified.

{% enddocs %}

{% docs bvs_ecosystems_effectiveness_score, %}

Effectiveness score.

{% enddocs %}

{% docs bvs_ecosystems_does_include_discovery %}

Includes discovery or not (boolean value: TRUE/FALSE).

{% enddocs %}

{% docs bvs_ecosystems_is_investment_modified %}

Investment modified or not (boolean value: TRUE/FALSE).

{% enddocs %}


{% docs bvs_ecosystems_updated_at %}

Latest edit date when the record was updated.

{% enddocs %}

{% docs bvs_ecosystems_latest_editor %}

Latest editor.

{% enddocs %}

{% docs bvs_ecosystems_models_edits %}

Models edit or not.

{% enddocs %}

{% docs bvs_ecosystems_presentation_name %}

Presentation name.

{% enddocs %}

{% docs bvs_ecosystems_viewer_logs %}

List of viewer logs.

{% enddocs %}


{% docs bvs_ecosystems_document_collaborator %}

Details of documents related to collaborators.

{% enddocs %}

{% docs bvs_ecosystems_access_level %}

Acess level.

{% enddocs %}

{% docs bvs_ecosystems_is_document_visible %}

Is document visible or not.

{% enddocs %}

{% docs bvs_ecosystems_user_id %}

Document collaborator document id.

{% enddocs %}

{% docs bvs_ecosystems_user_name %}

Document collaborator document user name.

{% enddocs %}


{% docs bvs_ecosystems_collaborator_id %}

Collaborator identificator.

{% enddocs %}