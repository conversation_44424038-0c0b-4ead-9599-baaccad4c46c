version: 2

models:
  - name: rpt_duo_license_utilization_monthly
    description: Workspace model for reporting on monthly Duo Pro seat utilization. Metric nuances described in column descriptions.
    data_tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - reporting_month
            - dim_subscription_id
            - add_on_name
            - product_deployment
            - paired_tier
    columns:
      - name: reporting_month
        description: Month of reported seats associated with Duo Pro subscription and usage behavior.
      - name: subscription_name
        description: '{{ doc("subscription_name") }}'
      - name: dim_subscription_id
        description: Identifier to join on 
      - name: crm_account_name
        description: '{{ doc("crm_account_name") }}'
      - name: dim_crm_account_id
        description: '{{ doc("dim_crm_account_id") }}'
      - name: dim_parent_crm_account_id
        description: '{{ doc("dim_parent_crm_account_id") }}'
      - name: product_deployment
        description: Using the deployment listed in product_rate_plan_name from mart_arr and renaming 'SaaS' to 'Gitlab.com' for clarity. 
      - name: add_on_name
        description: Duo Pro or Duo Enterprise.
      - name: paired_tier
        description: Tier or tiers associated with the Duo Pro Add On purchase with reported revenue in the same month that Duo Pro revenue is reported within mart_arr.
      - name: is_product_entity_associated_w_subscription
        description: FALSE if installation or namespace identifier cannot be mapped to subscription.
      - name: major_minor_version_id
        description: Latest major_minor_version_id reported for SM or Dedicated installation within a month. Gitlab.com deployments are always up to date and managed by the internal GitLab team.
      - name: paid_duo_seats
        description: quantity associated with Duo Pro subscription from mart_arr within a given reporting month.
      - name: count_seats_assigned
        description: DO NOT USE - Data team to create standard seat assignment reporting. Logic will be replaced here when trusted model is available. 
      - name: chat_active_users
        description: Calculation utilizes request_duo_chat_response in mart_behavior_structured_event, a snowplow data source for Gitlab.com chat utilization and redis_hll_counters.count_distinct_user_id_from_request_duo_chat_response_monthly a service ping data source for SM and Dedicated chat utilization. redis_hll_counters.count_distinct_user_id_from_request_duo_chat_response_monthly was implemented in GitLab version 16.11. To only include SM & Dedicated accounts eligible to report on this metric, filter to major_minor_version_id >= 1611. Array of namespaces that enabled event to occur are used to map the namespace to a subscription for GitLab.com deployments.
      - name: code_suggestions_active_users
        description: Calculation utilizes event_action = 'suggestion_requested' and app_id = 'gitlab_ai_gateway' from mart_behavior_structured_event_code_suggestion for all Deployment types. All ultimate_parent_namespace_ids and dim_installation_ids captured per event are flattened from their array format and joined to Duo Pro subscription data to achieve a utilization match. These entities captured represent how the the usage of code suggestions is enabled as opposed to WHERE code suggestion requests occurred. 
      - name: max_duo_active_users
        description: Because chat and code suggestions capture different user identifiers, an overall unique count cannot be calculated. This calculation takes the max count of users using code suggestions or chat as opposed to the total unique count of users. In the future a total unique count should be possible once a standard Cloud Connector event tracking methodology is available.
      - name: pct_usage_seat_utilization
        description: max_duo_active_users / paid_duo_seats
      - name: pct_assignment_seat_utilization
        description: DO NOT USE - Data team to create standard seat assignment reporting. Logic will be replaced here when trusted model is available. number_of_seats_assigned / paid_duo_seats This metric will equal 0 for all SM and Dedicated deployments since assigned seats are only captured for Gitlab.com accounts.
      - name: standard_pct_usage_seat_utilization
        description: max_duo_active_users / paid_duo_seats Result cannot exceed 100%.
      - name: standard_pct_assignment_seat_utilization
        description: DO NOT USE - Data team to create standard seat assignment reporting. Logic will be replaced here when trusted model is available. number_of_seats_assigned / paid_duo_seats Result cannot exceed 100%.
      - name: is_oss_or_edu_rate_plan
        description: Flag for rate plans containing OSS or EDU.
      - name: account_owner
        description: Current account_owner from dim_crm_account
      - name: parent_crm_account_geo
        description: Current parent_crm_account from dim_crm_account
      - name: parent_crm_account_sales_segment
        description: Current parent_crm_account_sales_segment from dim_crm_account
      - name: parent_crm_account_industry
        description: Current parent_crm_account_industry from dim_crm_account
      - name: technical_account_manager
        description: Current parent_crm_account_manager from dim_crm_account
      - name: turn_on_cloud_licensing
        description: Categories 'Offline', 'No', 'Yes' from Zuora subscription object
      - name: license_type
        description: License type derived from turn_on_cloud_licensing field, indicating whether system uses offline cloud licensing ('Offline'), legacy file-based licensing ('No'), or standard cloud licensing ('Yes', '').

  - name:  wk_rpt_gitlab_registered_users_monthly
    description: '{{ doc("wk_rpt_gitlab_registered_users_monthly") }}'
    data_tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - reporting_month
            - delivery_type
            - deployment_type
    columns:
      - name: reporting_month
        description: First day of the calendar month of the Service Ping and paid seats
      - name: is_first_day_of_last_month_of_fiscal_quarter
        description: '{{ doc("date_is_first_day_of_last_month_of_fiscal_quarter") }}'
      - name: fiscal_quarter_name_fy
        description: '{{ doc("date_fiscal_quarter_name_fy") }}'
      - name: delivery_type
        description: '{{ doc("product_delivery_type") }}'
      - name: deployment_type
        description: '{{ doc("product_deployment_type") }}'
      - name: total_user_count
        description: The total reported count of users, based on `instance_user_count` from the last ping of the month per installation
      - name: paid_user_count
        description: The count of paid users, defined as the count of paid seats from base products (i.e., not add-ons). In the event that `paid_user_count` is greater than `total_user_count` (ex. some edge cases with Dedicated), `paid_user_count` is set as `total_user_count`
      - name: free_user_count
        description: The count of free users, defined as `total_user_count - paid_user_count`

  - name: rpt_duo_license_utilization_weekly
    description: Workspace model for reporting on weekly Duo Pro seat utilization. Metric nuances described in column descriptions. 
    data_tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - reporting_week
            - dim_subscription_id
            - add_on_name
            - product_deployment
            - paired_tier
    columns:
      - name: reporting_week
        description: Week of reported seats associated with Duo Pro subscription and usage behavior starting on Monday.
      - name: subscription_name
        description: '{{ doc("subscription_name") }}'
      - name: dim_subscription_id
        description: Identifier to join on 
      - name: crm_account_name
        description: '{{ doc("crm_account_name") }}'
      - name: dim_crm_account_id
        description: '{{ doc("dim_crm_account_id") }}'
      - name: dim_parent_crm_account_id
        description: '{{ doc("dim_parent_crm_account_id") }}'
      - name: product_deployment
        description: Using the deployment listed in product_rate_plan_name from mart_arr and renaming 'SaaS' to 'Gitlab.com' for clarity. 
      - name: add_on_name
        description: Duo Pro or Duo Enterprise.
      - name: paired_tier
        description: Tier or tiers associated with the Duo Pro Add On purchase with reported revenue in the same week that Duo Pro revenue is reported within mart_arr.
      - name: is_product_entity_associated_w_subscription
        description: FALSE if installation or namespace identifier cannot be mapped to subscription.
      - name: major_minor_version_id
        description: Latest major_minor_version_id reported for SM or Dedicated installation within a week. Gitlab.com deployments are always up to date and managed by the internal GitLab team.
      - name: paid_duo_seats
        description: quantity associated with Duo Pro subscription from mart_arr within a given reporting week.
      - name: count_seats_assigned
        description: DO NOT USE - Data team to create standard seat assignment reporting. Logic will be replaced here when trusted model is available. 
      - name: chat_active_users
        description: Calculation utilizes request_duo_chat_response in mart_behavior_structured_event, a snowplow data source for Gitlab.com chat utilization and redis_hll_counters.count_distinct_user_id_from_request_duo_chat_response_monthly a service ping data source for SM and Dedicated chat utilization. redis_hll_counters.count_distinct_user_id_from_request_duo_chat_response_monthly was implemented in GitLab version 16.11. To only include SM & Dedicated accounts eligible to report on this metric, filter to major_minor_version_id >= 1611. Array of namespaces that enabled event to occur are used to map the namespace to a subscription for GitLab.com deployments.
      - name: code_suggestions_active_users
        description: Calculation utilizes event_action = 'suggestion_requested' and app_id = 'gitlab_ai_gateway' from mart_behavior_structured_event_code_suggestion for all Deployment types. All ultimate_parent_namespace_ids and dim_installation_ids captured per event are flattened from their array format and joined to Duo Pro subscription data to achieve a utilization match. These entities captured represent how the the usage of code suggestions is enabled as opposed to WHERE code suggestion requests occurred. 
      - name: max_duo_active_users
        description: Because chat and code suggestions capture different user identifiers, an overall unique count cannot be calculated. This calculation takes the max count of users using code suggestions or chat as opposed to the total unique count of users. In the future a total unique count should be possible once a standard Cloud Connector event tracking methodology is available.
      - name: pct_usage_seat_utilization
        description: max_duo_active_users / paid_duo_seats
      - name: pct_assignment_seat_utilization
        description: DO NOT USE - Data team to create standard seat assignment reporting. Logic will be replaced here when trusted model is available. number_of_seats_assigned / paid_duo_seats This metric will equal 0 for all SM and Dedicated deployments since assigned seats are only captured for Gitlab.com accounts.
      - name: standard_pct_usage_seat_utilization
        description: max_duo_active_users / paid_duo_seats Result cannot exceed 100%.
      - name: standard_pct_assignment_seat_utilization
        description: DO NOT USE - Data team to create standard seat assignment reporting. Logic will be replaced here when trusted model is available. number_of_seats_assigned / paid_duo_seats Result cannot exceed 100%.
      - name: is_oss_or_edu_rate_plan
        description: Flag for rate plans containing OSS or EDU.
      - name: account_owner
        description: Current account_owner from dim_crm_account
      - name: parent_crm_account_geo
        description: Current parent_crm_account from dim_crm_account
      - name: parent_crm_account_sales_segment
        description: Current parent_crm_account_sales_segment from dim_crm_account
      - name: parent_crm_account_industry
        description: Current parent_crm_account_industry from dim_crm_account
      - name: technical_account_manager
        description: Current parent_crm_account_manager from dim_crm_account
      - name: turn_on_cloud_licensing
        description: Categories 'Offline', 'No', 'Yes' from Zuora subscription object
      - name: license_type
        description: License type derived from turn_on_cloud_licensing field, indicating whether system uses offline cloud licensing ('Offline'), legacy file-based licensing ('No'), or standard cloud licensing ('Yes', '').

