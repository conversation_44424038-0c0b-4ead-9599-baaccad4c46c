version: 2

models:
    - name: snowflake_queries
      description: A table that can be used for reporting on query activity.
      columns:
        - name: query_id
          data_tests:
            - unique
            - not_null
        - name: dbt_metadata
          description: The metadata string added to the end of the query by the `query_comment` option in dbt.
        - name: dbt_version
          description: The version of dbt used for the invocation.
        - name: dbt_profile_name
          description: The profile used to identify connection details.
        - name: dbt_target_name
          description: The name of the target, within the profile, that dbt will use to make connections to the database.
        - name: dbt_target_user
          description: The name of the user, from the profile, that dbt will be executed as.
        - name: dbt_invocation_id
          description: A unique if for the execution of dbt.
        - name: dbt_run_started_at
          description: The timestamp the dbt invocation started at.
        - name: is_model_full_refresh
          description: A flag to indicate if a model is configured to full refresh.
        - name: is_invocation_full_refresh
          description: A flag to indicate if a dbt invocation was set to full refresh at run time.
        - name: model_materialization
          description: The selected materialization of the dbt model
        - name: dbt_runner
          description: A variable passed at run time to identify what started the dbt invocation.
        - name: resource_file
          description: The file that originates the dbt resource.
        - name: resource_id
          description: The unique node id of the dbt resource.
        - name: resource_name
          description: The name of the dbt resource.
        - name: resource_type
          description: The type of dbt resource.
        - name: package_name
          description: The name of the package of the dbt model
        - name: relation_database
          description: The database where the model is materialized
        - name: relation_schema
          description: The schema where the model is materialized
        - name: relation_identifier
          description: The table name of the model
        - name: dollars_spent
          description: The attributed credits of the query multiplied by the contract credit rate for the time period.
        - name: total_attributed_credits
          description: The number of credits attributed to the query. Based on the `snowflake_query_spend_attribution` model.
        - name: department
          description: The cost center department fo the snowflake user.
        - name: division
          description: The cost center division of the snowflake user.

    - name: snowflake_query_spend_attribution
      description: This model attributes the warehouse spend to the queries that execute within the warehouse spend window.
      columns: 
        - name: attribution_id
          description: '{{ doc("attribution_id") }}'
          data_tests:
            - unique
        - name: query_id
          description: '{{ doc("query_id") }}'
        - name: warehouse_id
          description: '{{ doc("warehouse_id") }}'
        - name: query_start_at
          description: '{{ doc("query_start_at") }}'
        - name: query_execution_start_at
          description: '{{ doc("query_execution_start_at") }}'
        - name: query_end_at
          description: '{{ doc("query_end_at") }}'
        - name: execution_time
          description: '{{ doc("execution_time") }}'
        - name: total_elapsed_time
          description: '{{ doc("total_elapsed_time") }}'
        - name: spend_start_at
          description: '{{ doc("warehouse_metering_start_at") }}'
        - name: spend_end_at
          description: '{{ doc("warehouse_metering_end_at") }}'
        - name: credits_used_total
          description: '{{ doc("credits_used_total") }}'
        - name: start_during_end_after
          description: '{{ doc("start_during_end_after") }}'
        - name: start_before_end_after
          description: '{{ doc("start_before_end_after") }}'
        - name: start_before_end_during
          description: '{{ doc("start_before_end_during") }}'
        - name: start_during_end_during
          description: '{{ doc("start_during_end_during") }}'
        - name: query_spend_duration
          description: '{{ doc("query_spend_duration") }}'
        - name: total_query_duration
          description: '{{ doc("total_query_duration") }}'
        - name: query_spend_fraction
          description: '{{ doc("query_spend_fraction") }}'
        - name: attributed_query_credits
          description: '{{ doc("attributed_query_credits") }}'

    - name: snowflake_query_metering
      description: This model aggregates the total credits attributed to a query and creates a table suitable for joining to a query.
      columns:
        - name: query_id
          data_tests:
            - unique
            - not_null
        - name: query_start_at
        - name: query_end_at
        - name: total_attributed_credits
          description: The aggregation of attributed credits from `snowflake_query_spend_attribution`

    - name: snowflake_clustering_spend
      description: This model calculates the spend associated with Snowflake's automatic clustering feature, including both credit usage and dollar amounts.
      tests:
        - dbt_utils.unique_combination_of_columns:
            combination_of_columns:
              - table_id
              - clustering_start_at

      columns:
        - name: table_id
          description: Internal/system-generated identifier for the table
        - name: full_table_name
          description: The full name of the table that underwent clustering
        - name: database_name
          description: The name of the database containing the clustered table
        - name: schema_name
          description: The name of the schema containing the clustered table
        - name: table_name
          description: The name of the table that underwent clustering
        - name: clustering_start_at
          description: The timestamp when the clustering operation started
        - name: clustering_end_at
          description: The timestamp when the clustering operation ended
        - name: bytes_reclustered
          description: The number of bytes that were reclustered during the operation
        - name: rows_reclustered
          description: The number of rows that were reclustered during the operation
        - name: credits_used
          description: The number of Snowflake credits used for the clustering operation
        - name: dollars_used
          description: The cost in dollars for the clustering operation, calculated by multiplying credits_used by the applicable contract rate
