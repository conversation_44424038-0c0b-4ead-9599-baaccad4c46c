version: 2

models:
  - name: rpt_user_request_crm_account_issue_epic
    description: Aggregation of mart_user_request, which is at the issue/epic||link grain at the issue/epic||crm_account grain.
    columns:
      - name: primary_key
        data_tests:
          - not_null
          - unique
  - name: rpt_user_request_issue_epic
    description: Aggregation of mart_user_request at the issue/epic grain.
    columns:
      - name: primary_key
        data_tests:
          - not_null
          - unique
  - name: rpt_product_usage_health_score_account_calcs
    description: This table calculates account-level scores for % of ARR reporting usage data, license utilization, User Engagement, SCM adoption, CI adoption, CD adoption, and Security Adoption. Its parent model, `rpt_product_usage_health_score` calculates these metrics at the installation/namespace-level. However, we need an installation/namespace-level with account-level metrics because in Tableau, end users interact with instance specific scores and require flexibility to compare account scores against specific instances per account. This table also includes records of accounts that do not have any associated Service Ping data.
    columns:
      - name: primary_key
        data_tests:
          - not_null
          - unique