version: 2

models:
  - name: gitlab_dotcom_groups
    description: '{{ doc("gitlab_dotcom_groups") }}'
    columns:
      - name: group_id
        data_tests:
          - not_null
          - unique
      - name: created_at
        data_tests:
          - not_null
      - name: updated_at
        data_tests:
          - not_null

  - name: gitlab_dotcom_namespace_historical_daily
    description: '{{ doc("gitlab_dotcom_namespace_historical_daily") }}'
    columns:
      - name: snapshot_day_namespace_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_namespace_lineage
    description: '{{ doc("gitlab_dotcom_namespace_lineage") }}'
    columns:
      - name: namespace_id
        data_tests:
          - not_null
          - unique
      - name: parent_id
        description: >-
          The direct parent of a namespace. Will only be null for top-level
          namespaces.
      - name: upstream_lineage
        description: >-
          An array of the namespace's parent (upstream) lineage. Direction from
          left to right is old to young [ultimate parent, ..., grandparent, parent, namespace]
        data_tests:
          - not_null
      - name: ultimate_parent_id
        description: >-
          The first (furthest left) item in upstream_lineage. I.E. the parent
          which has no parent. For top-level namespaces, this is will be
          itself.
        data_tests:
          - not_null
      - name: namespace_is_internal
        description: >-
          Whether or not the *ultimate parent* is a namespace that is internal to GitLab Inc.
        data_tests:
          - not_null
      - name: namespace_plan_id
        data_tests:
          - not_null
      - name: ultimate_parent_plan_id
        description: >-
          The plan_id of the namespace's ultimate parent namespace. This is inheritted downwards,
          meaning a namespace's ultimate_parent_plan_id will supersede its own plan_id whenever its plan
          is higher (ex: a Silver ultimate parent plan will override a namespace's Bronze plan.)

  - name: gitlab_dotcom_namespace_lineage_historical_daily
    columns:
      - name: snapshot_day_namespace_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          # - unique manually tested during development (2021-11-16) and found to pass.  Times out on XS warehouse.

  - name: gitlab_dotcom_namespace_lineage_historical_monthly

  - name: gitlab_dotcom_namespace_statistics_historical_monthly

  - name: gitlab_dotcom_namespace_storage_statistics_historical_monthly

  - name: gitlab_dotcom_namespace_lineage_scd
    description: >-
      A derivative table from the namespace snapshot table that identifies that valid time range for the
      namespace lineage of a given namespace.
    columns:
      - name: namespace_lineage_id
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_namespace_subscription_plan_scd
    description: >-
      A table that combines the namespace, namespace lineage, and subscription histories with the plan data.  
      The table is at the grain of namespace_id and a valid from `DATE`.  Note that the valid_from and valid_to
      fields are truncated from `TIMESTAMP` to `DATE`.
    data_tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - combined_valid_from
            - dim_namespace_id
    columns:
      - name: combined_valid_from
      - name: combined_valid_to
      - name: is_current
      - name: dim_namespace_id
      - name: namespace_name
      - name: namespace_path
      - name: owner_id
      - name: namespace_type
      - name: has_avatar
      - name: namespace_created_at
      - name: namespace_updated_at
      - name: is_membership_locked
      - name: has_request_access_enabled
      - name: has_share_with_group_locked
      - name: visibility_level
      - name: ldap_sync_status
      - name: ldap_sync_error
      - name: ldap_sync_last_update_at
      - name: ldap_sync_last_successful_update_at
      - name: ldap_sync_last_sync_at
      - name: lfs_enabled
      - name: shared_runners_enabled
      - name: shared_runners_minutes_limit
      - name: extra_shared_runners_minutes_limit
      - name: repository_size_limit
      - name: does_require_two_factor_authentication
      - name: two_factor_grace_period
      - name: project_creation_level
      - name: push_rule_id
      - name: namespace_lineage_id
      - name: namespace_id
      - name: parent_id
      - name: upstream_lineage
      - name: ultimate_parent_id
      - name: lineage_depth
      - name: gitlab_subscription_id
      - name: gitlab_subscription_start_date
      - name: gitlab_subscription_end_date
      - name: gitlab_subscription_trial_ends_on
      - name: max_seats_used
      - name: seats
      - name: is_trial
      - name: gitlab_subscription_created_at
      - name: gitlab_subscription_updated_at
      - name: seats_in_use
      - name: seats_owed
      - name: trial_extension_type
      - name: ultimate_parent_plan_id
      - name: plan_title
      - name: plan_is_paid
      - name: plan_name
      - name: plan_name_modified
      - name: namespace_is_internal