version: 2

models:
    - name: netsuite_actuals_balance_sheet
      description: Balance Sheet from Netsuite
      columns:
        - name: unique_account_name
          data_tests:
            - not_null
        - name: accounting_period
          data_tests:
            - not_null
        - name: account_type
          data_tests:
            - not_null
    - name: netsuite_transaction_lines_xf
      description: The base transaction model enriched with account data and masking some sensitive data.
      columns:
        - name: transaction_lines_unique_id
          data_tests:
            - not_null
            - unique
        - name: transaction_id
          data_tests:
            - not_null
        - name: transaction_line_id
          data_tests:
            - not_null
        - name: account_id
          data_tests:
            - relationships:
                to: ref('netsuite_accounts')
                field: account_id
        - name: department_id
          data_tests:
            - relationships:
                to: ref('netsuite_departments')
                field: department_id
        - name: subsidiary_id
          data_tests:
            - not_null
            - relationships:
                to: ref('netsuite_subsidiaries')
                field: subsidiary_id
        - name: amount
          data_tests:
            - not_null
        - name: gross_amount
          data_tests:
            - not_null
        - name: account_name
        - name: account_full_name
        - name: account_full_description
        - name: account_number
        - name: account_type
        - name: subsidiary_name
        - name: memo
