version: 2

models:
  - name: snowflake_show_roles
    description: This is the latest snapshot of Snowflake roles.
    columns:
        -   name: role_name
            data_tests:
                - not_null
  - name: snowflake_show_users
    description: This is the latest snapshot of Snowflake users.
    columns:
        -   name: user_name
            data_tests:
                - not_null
  - name: snowflake_grants_to_role
    description: This is the latest snapshot of users and the roles they've been granted.
    columns:
        -   name: role_name
            data_tests:
                - not_null
        -   name: granted_to_type
            data_tests:
                - not_null
        -   name: grantee_user_name
            data_tests:
                - not_null
  - name: snowflake_grants_to_user
    description: This is the latest snapshot of users and the roles they've been granted.
    columns:
        -   name: role_name
            data_tests:
                - not_null
        -   name: granted_to_type
            data_tests:
                - not_null
        -   name: grantee_name
            data_tests:
                - not_null   
