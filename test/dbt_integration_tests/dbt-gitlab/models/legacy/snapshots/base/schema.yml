version: 2

models:
  - name: bizible_attribution_touchpoint_snapshots_source
    description: Attribution touchpoints from Bizible loaded into Salesforce.
  - name: bizible_touchpoint_snapshots_source
    description: Attribution touchpoints from Bizible loaded into Salesforce.
  - name: customers_db_customers_snapshots_base
    description: This is the base model for snapshotted customers data from the customers.gitlab.com database.
    columns:
      - name: customer_snapshot_id
        description: Auto-generated primary key when building the snapshot with dbt
        data_tests:
          - not_null
          - unique
      - name: customer_id
        data_tests:
          - not_null
      - name: valid_from
        data_tests:
          - not_null
      - name: valid_to
  - name: customers_db_orders_snapshots_base
    description: This is the base model for snapshotted orders data from the customers.gitlab.com database.
    columns:
      - name: order_snapshot_id
        description: Auto-generated primary key when building the snapshot with dbt
        data_tests:
          - not_null
          - unique
      - name: order_id
        data_tests:
          - not_null
      - name: valid_from
        data_tests:
          - not_null
      - name: valid_to

  - name: gitlab_dotcom_application_settings_snapshots_base
    description: This is the base model for snapshotted gitlab application settings data from the gitlab.com database.
    columns:
      - name: application_settings_snapshot_id
        description: Auto-generated primary key when building the snapshot with dbt.
        data_tests:
          - not_null
          - unique
      - name: application_settings_id
        data_tests:
          - not_null
  - name: gitlab_dotcom_gitlab_subscriptions_snapshots_base
    description: This is the base model for snapshotted gitlab subscriptions data from the gitlab.com database.
    columns:
      - name: gitlab_subscription_snapshot_id
        description: Auto-generated primary key when building the snapshot with dbt
        data_tests:
          - not_null
          - unique
      - name: gitlab_subscription_id
        data_tests:
          - not_null
      - name: valid_from
        data_tests:
          - not_null
      - name: valid_to
  - name: gitlab_dotcom_members_snapshots_base
    description: This is the base model for snapshotted members data from the gitlab.com database.
    columns:
      - name: member_snapshot_id
        description: Auto-generated primary key when building the snapshot with dbt
        data_tests:
          - not_null
          - unique
      - name: member_id
        data_tests:
          - not_null
      - name: valid_from
        data_tests:
          - not_null
      - name: valid_to
  - name: gitlab_dotcom_namespaces_snapshots_base
    description: This is the base model for snapshotted gitlab subscriptions data from the gitlab.com database.
    columns:
      - name: namespace_snapshot_id
        description: Auto-generated primary key when building the snapshot with dbt
        data_tests:
          - not_null
          - unique
      - name: namespace_id
        data_tests:
          - not_null
      - name: valid_from
        data_tests:
          - not_null
      - name: valid_to

  - name: gitlab_dotcom_project_ci_cd_settings_snapshots_base
    description: '{{ doc("gitlab_dotcom_project_ci_cd_settings_snapshots_base") }}'
    columns:
      - name: project_ci_cd_settings_snapshot_pk
        data_tests:
          - not_null
          - unique
      - name: project_ci_cd_settings_snapshot_id
        description: Auto-generated primary key when building the snapshot with dbt
        data_tests:
          - not_null
      - name: project_id
        data_tests:
          - not_null

  - name: gitlab_dotcom_project_statistics_snapshots_base
    description: Base model for Gitlab.com project statistics snapshot
    columns:
      - name: project_statistics_id
        data_tests:
          - not_null
      - name: project_id
        data_tests:
          - not_null

  - name: gitlab_dotcom_namespace_details_snapshots_source
    description: Source model for Gitlab.com namespace details snapshot

  - name: gitlab_dotcom_subscription_add_on_purchases_snapshots_base
    description: Source model for Gitlab.com namespace details snapshot
    columns:
      - name: id
        data_tests:
          - not_null
      - name: subscription_add_on_id
        data_tests:
          - not_null

  - name: customers_db_licenses_snapshots_base
    description: This is the base model for snapshots from the licenses table from the customers database.
    columns:
      - name: license_snapshot_id
        description: Auto-generated primary key when building the snapshot with dbt.
        data_tests:
          - not_null
          - unique
      - name: license_id
        data_tests:
          - not_null
      - name: valid_from
        data_tests:
          - not_null
      - name: valid_to

  - name: sfdc_account_snapshots_source
    description: This is the source model for snapshots of the salesforce account table for cleaning and renaming.
    data_tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - account_id
            - dbt_valid_from
    columns:
      - name: account_id
        data_tests:
          - not_null
      - name: dbt_valid_from
        data_tests:
          - not_null
      - name: zoom_info_company_name
        description: The name of the company matched by the Zoom Info enrichment process.
      - name: zoom_info_company_revenue
        description: The estimated revenue of the company matched by the Zoom Info enrichment process.
      - name: zoom_info_company_employee_count
        description: The number of employees of the company matched by the Zoom Info enrichment process.
      - name: zoom_info_company_industry
        description: The primary industry of the company matched by the Zoom Info enrichment process.
      - name: zoom_info_company_city
        description: The city of the company matched by the Zoom Info enrichment process.
      - name: zoom_info_company_state_province
        description: The state or province of the company matched by the Zoom Info enrichment process.
      - name: zoom_info_company_country
        description: The country of the company matched by the Zoom Info enrichment process.
      - name: is_excluded_from_zoom_info_enrich
        description: A flag to indicate if the account should be included in the Zoom Info enrichment process.
      - name: bdr_prospecting_status
        description: Indicates whether the account is actively being prospected by a BDR.
      - name: has_six_sense_6_qa
        description: The 6QA status of the account for the product.
      - name: risk_rate_guid
        description: The globally unique ID (GUID) for the third party record in NavEx RiskRate.
      - name: six_sense_account_profile_fit
        description: A measure of how similar a company is to the ideal customer profile.
      - name: six_sense_account_reach_score
        description: A measure of current outreach activities against optimal quality.
      - name: six_sense_account_profile_score
        description: A measure of how similar a company is to the ideal customer profile.
      - name: six_sense_account_buying_stage
        description: The 6QA buting stage of the account
      - name: six_sense_account_numerical_reach_score
        description:  measure of current outreach activities against optimal quality.
      - name: six_sense_account_update_date
        description: The latest update date and time when 6sense Scores were updated.
      - name: six_sense_account_6_qa_end_date
        description: The date the account was disqualified from 6QA status 
      - name: six_sense_account_6_qa_age_days
        description: The age of the Account's 6QA data, in days. 
      - name: six_sense_account_6_qa_start_date
        description: The date the account qualified for 6QA status 
      - name: six_sense_account_intent_score
        description: A measure, between 0 and 100, of how much an account is behaving like accounts with which there is a history of previously opened opportunities.
      - name: six_sense_segments
        description: The Account's 6QA segment. 
      - name: gs_health_csm_sentiment
        description: This is what the TAM thinks the health of this account should be - Formerly was just Health Score - Gainsight is the SSOT for this field and its value can only be updated in Gainsight.
      - name: executive_sponsor_id
        description: sfdc_user_id of an executive sponsor for this account
      - name: gs_csm_compensation_pool
        description: CSM compensation pool for the account
      - name: groove_notes
        description: contains notes that are surfaced in Groove's sidebar by default
      - name: groove_engagement_status
        description: Indicates the status of CUSTOMER engagement such as email opens, phone conversations or meetings. The threshold between "recent" and "past" can be customized
      - name: groove_inferred_status
        description: Indicates the account status in the typical customer life cycle of an account. The status is automatically inferred by looking at activities, engagement signals and opportunities
      - name: compensation_target_account
        description: This field is used for compensation purposes only to identify pre-approved new logo target accounts
      - name: ptp_insights
        description: Provides detailed insights about factors influencing an account's Propensity to Purchase
      - name: ptp_score_value
        description: A numerical representation of the PTP score, ranging from 1 to 5, indicating the likelihood of purchase
      - name: ptp_score
        description: A visual representation of the Propensity to Purchase score
      - name: pubsec_type
        description: Identifies the account type as US-PubSec (public sector in the US), ROW-PubSec (public sector in the rest of the world), or non-PubSec.


  - name: sfdc_bizible_attribution_touchpoint_snapshots_source
    description: Attribution touchpoints from Bizible loaded into Salesforce.
  - name: sfdc_bizible_touchpoint_snapshots_source
    description: Attribution touchpoints from Bizible loaded into Salesforce.  
  - name: sfdc_opportunity_product_snapshots_source
    description: This is the source model for snapshots of salesforce opportunity products
    data_tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - opportunity_product_id
            - dbt_valid_from
    columns:
      - name: opportunity_product_id
        data_tests:
          - not_null
      - name: opportunity_id
        data_tests:
          - not_null
      - name: dbt_valid_from
        data_tests:
          - not_null

  - name: sfdc_contact_snapshots_source
    description: This is the source model for snapshots of the salesforce contact table.
    data_tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - contact_id
            - dbt_valid_from
    columns:
      - name: contact_id
        data_tests:
          - not_null
      - name: dbt_valid_from
        data_tests:
          - not_null

  - name: sfdc_lead_snapshots_source
    description: This is the source model for snapshots of the salesforce lead table.
    data_tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - lead_id
            - dbt_valid_from
    columns:
      - name: lead_id
        data_tests:
          - not_null
      - name: dbt_valid_from
        data_tests:
          - not_null

  - name: sfdc_user_snapshots_source
    description: This is the source model for snapshots of the salesforce user table for cleaning and renaming.
    columns:
      - name: id
          - not_null
      - name: dbt_valid_from
          - not_null       
  - name: zuora_account_snapshots_source
    description: Source layer for Zuora Accounts Snapshots for cleaning and renaming
  - name: zuora_amendment_snapshots_source
    description: Source layer for Zuora Amendments Snapshots for cleaning and renaming
  - name: zuora_contact_snapshots_source
    description: Source layer for Zuora Contacts Snapshots for cleaning and renaming
  - name: zuora_invoice_snapshots_source
    description: Source layer for Zuora Invoices Snapshots for cleaning and renaming
  - name: zuora_invoice_item_snapshots_source
    description: Source layer for Zuora Invoice Items Snapshots for cleaning and renaming
  - name: zuora_product_snapshots_source
    description: Source layer for Zuora Products Snapshots for cleaning and renaming
  - name: zuora_rate_plan_snapshots_source
    description: Source layer for Zuora Rate Plans Snapshots for cleaning and renaming
  - name: zuora_rate_plan_charge_snapshots_source
    description: Source layer for Zuora Rate Plan Charges Snapshots for cleaning and renaming
  - name: zuora_subscription_snapshots_source
    description: Source layer for Zuora Subscriptions
  - name: zuora_query_api_charge_metrics_snapshot_source
    description: Source layer for Zuora Charge Metrics for cleaning and renaming

  - name: sfdc_sandbox_user_snapshots_source

  - name: customers_db_billing_accounts_snapshots_base
    description: This is the base model for snapshotted billing accounts data from the customers.gitlab.com database.
    columns:
      - name: billing_account_snapshot_id
        description: Auto-generated primary key when building the snapshot with dbt
        data_tests:
          - not_null
          - unique
      - name: billing_account_id
        description: A system-generated unique ID for billing accounts in CDot
        data_tests:
          - not_null
      - name: zuora_account_id
        description: A unique Zuora account number associated with the User
      - name: zuora_account_name
        description: The name of the zuora account associated with the zuora account id
      - name: sfdc_account_id
        description: The customer account ID for the account
      - name: billing_account_created_at
        description: The date the billing account was created for the customer
      - name: billing_account_created_at
        description: The date when any changes happened to the Billing account record for a customer
      - name: valid_from
        data_tests:
          - not_null
      - name: valid_to

  - name: customers_db_billing_account_contacts_snapshots_base
    description: This is the base model for snapshotted billing account contacts data from the customers.gitlab.com database.
    columns:
      - name: billing_account_contact_snapshot_id
        description: Auto-generated primary key when building the snapshot with dbt
        data_tests:
          - not_null
          - unique
      - name: work_email
        description: Billing account contact's work email
      - name: zuora_account_id
        description: Zuora account id associated with the Billing Account Contact
        data_tests:
          - not_null
      - name: zuora_contact_id
        description: Zuora contact id associated with the Billing Account Contact
        data_tests:
          - not_null       
      - name: billing_account_contact_created_at
        description: The date the billing account contact was created
      - name: billing_account_contact_updated_at
        description: The date the billing account contact was updated
        data_tests:
          - not_null
      - name: valid_from
        data_tests:
          - not_null
      - name: valid_to


  - name: customers_db_cloud_activations_snapshots_base
    description: This is the base model for snapshotted cloud activations data from the customers.gitlab.com database. 
    columns:
      - name: cloud_activation_snapshot_id
        description: Auto-generated primary key when building the snapshot with dbt
        data_tests:
          - not_null
          - unique
      - name: cloud_activation_id
        description: A system-generated unique ID for Cloud activations in CDot
        data_tests:
          - not_null
      - name: customer_id
        description: Membership Customer ID
        data_tests:
          - not_null
      - name: billing_account_id
        description: Membership Account ID. Join key between Customers and billings_accounts.
      - name: subscription_name
        description: Zuora Subscription name
      - name: is_super_sonics_aware_subscription
        description: Identifies Super Sonics subscriptions.
      - name: seat_utilization_reminder_sent_at
        description: Timestamp when reminder email to submit seat utilization data was sent.
      - name: cloud_activation_created_at
        data_tests:
          - not_null
      - name: cloud_activation_updated_at
        data_tests:
          - not_null
      - name: valid_from
        data_tests:
          - not_null
      - name: valid_to

  - name: gitlab_dotcom_pipl_users_snapshots_source
    description: ' {{ doc("gitlab_dotcom_pipl_users_snapshots_source") }} '
    columns:
      - name: user_id
        description: ' {{ doc("dim_user_id") }} '
      - name: initial_email_sent_at
        description: Date last email was sent to user
      - name: last_access_from_pipl_country_at
        description: Date the user last accessed from a PIPL country
      - name: created_at
        description: ' {{ doc("user_created_at") }} '
      - name: updated_at
      - name: _last_dbt_run
        description: Date of last dbt run
      - name: dbt_scd_id
        description: ' {{ doc("dbt_scd_id") }} '
      - name: dbt_updated_at
        description: ' {{ doc("dbt_updated_at_snapshot_model") }} '
      - name: valid_from
        description: ' {{ doc("dbt_valid_from") }} '
      - name: valid_to
        description: ' {{ doc("dbt_valid_to") }} '

