version: 2

models:
    - name: date_details
      description: Join on this table to add additional date complexity
      columns:
          - name: date_day
            description: Calendar date, e.g. '2019-02-02'
            data_tests:
               - not_null
               - unique
          - name: date_actual
            description: Matches `date_day`, duplicated for ease of use
            data_tests:
               - not_null
               - unique
          - name: day_name
            description: Abbreviated name of the day of the week, e.g. 'Sat' for 2019-02-02
            data_tests:
               - not_null
          - name: month_actual
            description: Number for the calendar month of the year, e.g. '2' for 2019-02-02
            data_tests:
               - not_null
          - name: year_actual
            description: Calendar year, e.g. '2019' for 2019-02-02
            data_tests:
               - not_null
          - name: quarter_actual
            description: Calendar quarter, e.g. '1' for 2019-02-02
            data_tests:
               - not_null
          - name: day_of_week
            description: Number of the day of the week, with Sunday = 1 and Saturday = 7
            data_tests:
               - not_null
          - name: first_day_of_week
            description: Calendar date of the first Sunday of that week, e.g. '2019-01-27' for 2019-02-02
            data_tests:
               - not_null
          - name: week_of_year
            description: Calendar week of year, e.g. '5' for 2019-02-02
            data_tests:
               - not_null
          - name: day_of_month
            description: Day Number of the month, e.g. '2' for 2019-02-02
            data_tests:
               - not_null
          - name: day_of_quarter
            description: Day Number from the start of the calendar quarter, e.g. '33' for 2019-02-02
            data_tests:
               - not_null
          - name: day_of_year
            description: Day Number from the start of the calendar year, e.g. '33' for 2019-02-02
            data_tests:
               - not_null
          - name: fiscal_year
            description: Fiscal year for the date, e.g. '2020' for 2019-02-02
            data_tests:
               - not_null
          - name: fiscal_quarter
            description: Fiscal quarter for the date, e.g. '1' for 2019-02-02
            data_tests:
               - not_null
          - name: day_of_fiscal_quarter
            description: Day Number from the start of the fiscal quarter, e.g. '2' for 2019-02-02
            data_tests:
               - not_null
          - name: day_of_fiscal_year
            description: Day Number from the start of the fiscal year, e.g. '2' for 2019-02-02
            data_tests:
               - not_null
          - name: month_name
            description: The full month name for any calendar month, e.g. 'February' for 2019-02-02
            data_tests:
               - not_null
          - name: first_day_of_month
            description: The first day of a calendar month, e.g. '2019-02-01' for 2019-02-02
            data_tests:
               - not_null
          - name: last_day_of_month
            description: The last day of a calendar month, e.g. '2019-02-28' for 2019-02-02
            data_tests:
               - not_null
          - name: first_day_of_year
            description: The first day of a calendar year, e.g. '2019-01-01' for 2019-02-02
            data_tests:
               - not_null
          - name: last_day_of_year
            description: The last day of a calendar year, e.g. '2019-12-31' for 2019-02-02
            data_tests:
               - not_null
          - name: first_day_of_quarter
            description: The first day of a calendar quarter, e.g. '2019-01-01' for 2019-02-02
            data_tests:
               - not_null
          - name: last_day_of_quarter
            description: The last day of a calendar quarter, e.g. '2019-03-31' for 2019-02-02
            data_tests:
               - not_null
          - name: first_day_of_fiscal_quarter
            description: The first day of the fiscal quarter, e.g. '2019-02-01' for 2019-02-02
            data_tests:
               - not_null
          - name: last_day_of_fiscal_quarter
            description: The last day of the fiscal quarter, e.g. '2019-04-30' for 2019-02-02
            data_tests:
               - not_null
          - name: first_day_of_fiscal_year
            description: The first day of the fiscal year, e.g. '2019-02-01' for 2019-02-02
            data_tests:
               - not_null
          - name: last_day_of_fiscal_year
            description: The last day of the fiscal year, e.g. '2020-01-31' for 2019-02-02
            data_tests:
               - not_null
          - name: week_of_fiscal_year
            description: The week number for the fiscal year, e.g. '1' for 2019-02-02
            data_tests:
               - not_null
          - name: month_of_fiscal_year
            description: The month number for the fiscal year, e.g. '1' for 2019-02-02
            data_tests:
               - not_null
          - name: last_day_of_week
            description: The Saturday of the week, e.g. '2019-02-02' for 2019-02-02
            data_tests:
               - not_null
          - name: quarter_name
            description: The name of the calendar quarter, e.g. '2019-Q1' for 2019-02-02
            data_tests:
               - not_null
          - name: fiscal_quarter_name
            description: The name of the fiscal quarter, e.g '2020-Q1' for 2019-02-02
            data_tests:
               - not_null
          - name: fiscal_quarter_name_fy
            description: The name of the fiscal quarter, e.g 'FY20-Q1' for 2019-02-02
            data_tests:
               - not_null
          - name: holiday_desc
            description: The name of the holiday, if applicable
          - name: is_holiday
            description: Whether or not it is a holiday
            data_tests:
               - not_null
