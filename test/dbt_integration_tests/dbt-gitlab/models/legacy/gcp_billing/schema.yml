version: 2

models:
  - name: gcp_billing_export_xf
    description: Final model for billing export.
    columns:
      - name: source_primary_key
        data_tests:
          - not_null

  - name: gcp_billing_export_credits
    description: table for unnested credit rows
    columns:
      - name: credit_pk
        data_tests:
          - not_null
      - name: source_primary_key
        data_tests:
          - not_null

  - name: gcp_billing_export_resource_labels
    description: table for unnested resource label rows
    columns:
      - name: resource_label_pk
        data_tests:
          - not_null
      - name: source_primary_key
        data_tests:
          - not_null

  - name: gcp_billing_export_project_labels
    description: table for unnested project label rows
    columns:
      - name: project_label_pk
        data_tests:
          - not_null
      - name: source_primary_key
        data_tests:
          - not_null

  - name: gcp_billing_export_system_labels
    description: table for unnested system label rows
    columns:
      - name: system_label_pk
        data_tests:
          - not_null
      - name: source_primary_key
        data_tests:
          - not_null

  - name: gcp_billing_detailed_export_xf
    description: Final model for billing export.
    columns:
      - name: source_primary_key
        data_tests:
          - not_null

  - name: gcp_billing_detailed_export_credits
    description: table for unnested credit rows
    columns:
      - name: credit_pk
        data_tests:
          - not_null
      - name: source_primary_key
        data_tests:
          - not_null

  - name: gcp_billing_detailed_export_resource_labels
    description: table for unnested resource label rows
    columns:
      - name: resource_label_pk
        data_tests:
          - not_null
      - name: source_primary_key
        data_tests:
          - not_null

  - name: gcp_billing_detailed_export_project_labels
    description: table for unnested project label rows
    columns:
      - name: project_label_pk
        data_tests:
          - not_null
      - name: source_primary_key
        data_tests:
          - not_null

  - name: gcp_billing_detailed_export_system_labels
    description: table for unnested system label rows
    columns:
      - name: system_label_pk
        data_tests:
          - not_null
      - name: source_primary_key
        data_tests:
          - not_null
