version: 2

models:
  - name: rpt_delta_arr_parent_month_8th_calendar_day
    description: '{{ doc("rpt_delta_arr_parent_month_8th_calendar_day") }}'

  - name: rpt_delta_arr_parent_product_month_8th_calendar_day
    description: '{{ doc("rpt_delta_arr_parent_product_month_8th_calendar_day") }}'

  - name: rpt_crm_opportunity_snapshot_8th_calendar_day
    description: Snapshot of mart_crm_opportunity_daily_snapshot taken on the 8th calendar day after month-end close.
    columns:
      - name: crm_opportunity_snapshot_id
        data_tests:
          - not_null
          - unique
    
  - name: rpt_crm_opportunity_renewal
    description: '{{ doc("rpt_crm_opportunity_renewal") }}'

  - name: rpt_crm_opportunity_open
    description: '{{ doc("rpt_crm_opportunity_open") }}'

  - name: rpt_gtm_crm_actuals
    description: A report table with the GTM actuals contained in the GTM Operating Performance Tableau dashboard.

  - name: rpt_gtm_pivoted_targets
    description: Pivoted targets for the GTM Operating Performance KPI's - putting each KPI's target into its own column, instead of the kpi_name format.

  - name: rpt_gtm_scaffold
    description: A scaffold table which contains all of the needed combinations of dimensions for joining (via a Tableau relationship) the GTM actuals and targets report tables.

  - name: rpt_stage_progression
    description: '{{ doc("rpt_stage_progression") }}' 
    columns: 
      - name: dim_crm_opportunity_id
        data_tests:
          - not_null
          - unique

  - name: rpt_parent_delta_arr_snapshot_combined
    description: A report table which contains ARR and Quantity at the level of the parent account, and includes the delta for the ARR and Quantity change at the parent account level from the prior month, quarter, and year for delta reporting.

  - name: rpt_pipeline_coverage_daily_normalised_180_days
    description: '{{ doc("rpt_pipeline_coverage_daily_normalised_180_days") }}'
    columns:
      - name: rpt_pipeline_coverage_daily_pk
        data_tests:
          - not_null
          - unique
      - name: future_quarters_current_date_normalised_180_days
        description:
          The field returns the corresponding snapshot_date_normalized_180_days to the most recent snapshot date in a future quarter, facilitating current day selection in future quarters in Tableau.
      - name: most_recent_snapshot_date_normalised_180_days
        description:
          The field returns the corresponding snapshot_date_normalized_180_days to the most recent snapshot date in current quarter, facilitating current day selection in current and past quarters in Tableau.

  - name: rpt_pipeline_coverage_daily
    description: '{{ doc("rpt_pipeline_coverage_daily") }}'
    columns:
      - name: rpt_pipeline_coverage_daily_pk
        data_tests:
          - not_null
          - unique

  - name: rpt_pipeline_coverage
    description: '{{ doc("rpt_pipeline_coverage") }}'

  - name: rpt_sales_clari_net_arr_forecast
    description: '{{ doc("rpt_sales_clari_net_arr_forecast") }}'
    columns:
    - name: forecast_id
      description: "Unique identifier for each forecast entry"
    - name: user_full_name
      description: "Full name of the user who created the forecast"
    - name: user_email
      description: "Email address of the user who created the forecast"
    - name: crm_user_id
      description: "CRM system's unique identifier for the user"
    - name: sales_team_role
      description: "User's role within the sales team"
    - name: parent_role
      description: "Parent or supervisor role in the sales hierarchy"
    - name: fiscal_quarter
      description: "Fiscal quarter the forecast applies to"
    - name: field_name
      description: "Name of the forecast field being populated"
    - name: week_number
      description: "Sequential week number within the fiscal period"
    - name: week_start_date
      description: "Start date of the forecast week"
    - name: week_end_date
      description: "End date of the forecast week"
    - name: field_type
      description: "Type classification of the forecast field"
    - name: forecast_value
      description: "Numerical value of the forecast"
    - name: is_updated
      description: "Boolean indicating if forecast has been updated"

  - name: rpt_targets_actuals_multi_grain_daily
    description: '{{ doc("rpt_targets_actuals_multi_grain_daily") }}'
