version: 2

models:
  - name: mart_available_to_renew
    description: This model generates a list of subscriptions which will be available to renew in a given time period based on existing charges and term dates. It also has logic built in to identify multi-year bookings which is not yet avilable in the source data.
    columns:
      - name: primary_key
        description: Composite key for the model combining the unique identifier of the charge as well as the month the charge is effective in the term.
        data_tests:
          - not_null
          - unique
      - name: fiscal_year
        description: Fiscal year when the subscription will be available to renew.
        data_tests:
          - not_null
        tags: ["tdf", "mart", "atr"]
      - name: fiscal_quarter_name_fy
        description: Fiscal quarter when the subscription will be available to renew.
      - name: first_day_of_fiscal_quarter
        description: Calendar date of first day of fiscal quarter.
      - name: close_month
        description: Month when the opportunity associated with the subscription closed based on its `close_date`.
      - name: dim_charge_id
        description: Unique identifier of a rate plan charge associated with the subscription.
      - name: dim_crm_opportunity_id
        description: Unique identifier of the crm opportunity associated with the subscription.
      - name: dim_crm_account_id
        description: Unique identifier of the crm account associated with the subscription.
      - name: dim_billing_account_id
        description: Unique identifier of the billing account associated with the subscription.
      - name: dim_subscription_id
        description: Unique identifier of a subscription.
        data_tests:
          - not_null
        tags: ["tdf", "mart", "atr"]
      - name: dim_product_detail_id
        description: Unique identifier of a product rate plan charge associated with the subscription.
      - name: subscription_name
        description: Name applied to the subscription.
      - name: subscription_start_month
        description: Month when the subscription is set to begin.
      - name: subscription_end_month
        description: Month when the subscription is set to end.
      - name: term_start_month
        description: Month when a subscription term begins.
      - name: renewal_month
        description: Month when a subscription term ends.
        data_tests:
          - not_null
        tags: ["tdf", "mart", "atr"]
      - name: bookings_term_end_month
        description: Similar to the term end month but calculates intermediate term end months for multi year subscriptions with terms greater than 12 months. These end months do not exist in the Zuora billing system. This allows us to get a view of all subscriptions renewing on a yearly basis.
      - name: multi-year_booking_subscription_end_month
        description: Month when the final subscription in a multi-year booking is set to end.
      - name: last_paid_month_in_term
        description: Last month charge is applied for a charge's term.
      - name: current_term
        description: Duration of the subscription's current term.
      - name: zuora_renewal_subscription_name
        description: Name of a renewal subscription associated with the current subscription.
      - name: renewal_subscription_end_month
        description: End month of the renewal subscription associated with the current subscription.
      - name: parent_crm_account_name
        description: Name of the ultimate parent crm account associated with the subscription.
      - name: crm_account_name
        description: Name of the crm account associated with the subscription.
      - name: parent_crm_account_sales_segment
        description: Segment of the ultimate parent crm account associated with the subscription.
      - name: dim_crm_user_id
        description: Unique identifier of the account owner associated with the subscription.
      - name: user_name
        description: Name of the account owner associated with the subscription.
      - name: user_role_id
        description: Unique identifier of the role of the account owner associated with the subscription.
      - name: crm_user_sales_segment
        description: Segment of the account owner associated with the subscription.
      - name: crm_user_geo
        description: Geo of the account owner associated with the subscription.
      - name: crm_user_region
        description: Region of the account owner associated with the subscription.
      - name: crm_user_area
        description: Area of the account owner associated with the subscription.
      - name: product_tier_name
        description: Tier of the product on the charge.
      - name: product_delivery_type
        description: Delivery type of the product on the charge.
      - name: renewal_type
        description: Type of renewal (multi-year_booking or not)
      - name: is_multi_year_booking
        description: Flag indicating if a subscription is multi-year or not.
      - name: is_multi_year_booking_with_multi_subs
        description: Flag indicating if a subscription is multi-year with multiple associated subscriptions.
      - name: subscription_term
        description: Current term of the subscription.
      - name: estimated_total_future_billings
        description: Estimate of total amount to be billed in the future for a given charge.
      - name: is_available_to_renew
        description: Flag indicating if the subscription is available to renew in the period.
      - name: opportunity_term_group
        description: Duration of the opportunity in years based on the opportunity's term.
      - name: number_of_seats
        description: a sum of the quantity where the unit of measurement is 'Seats'.
      - name: arr
        description: Annual recurring revenue associated with the subscription.

  - name: mart_available_to_renew_snapshot_model
    description: Snapshot model for `mart_available_to_renew` expanded to the daily grain. This models shows the state of what was available to renew on any given day based on snapshot data from the base `mart_available_to_renew` model.
    columns:
        - name: primary_key
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "atr" ]

  - name: mart_available_to_renew_variance
    description: '{{ doc("mart_available_to_renew_variance") }}'
    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - fiscal_quarter_name_fy
            - dim_crm_account_id
    columns:
        - name: fiscal_quarter_name_fy
        - name: dim_crm_account_id
        - name: available_to_renew
        - name: arr_basis


  - name: mart_processed_refunds_monthly
    description: '{{ doc("mart_processed_refunds_monthly") }}'
    columns:
        - name: period
          description: Accounting period
          data_tests:
            - not_null
            - unique
        - name: refund_amount
          description: Sum of refund amounts per period
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]

  - name: mart_processed_iia_monthly
    description: '{{ doc("mart_processed_iia_monthly") }}'
    columns:
        - name: period
          description: Accounting period
          data_tests:
            - not_null
            - unique

  - name: mart_payments_refunds_against_future_invoices
    description: '{{ doc("mart_payments_refunds_against_future_invoices") }}'
    columns:
        - name: period
          description: Accounting period
          data_tests:
            - not_null
            - unique
        - name: payments_refunds_against_future_invoices
          description: Sum of payment and refund amounts made for future dated invoices
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]

  - name: mart_processed_cba_monthly
    description: '{{ doc("mart_processed_cba_monthly") }}'
    columns:
        - name: period
          description: Accounting period
          data_tests:
            - not_null
            - unique
        - name: increase
          description: Sum of increased credit balance
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]
        - name: decrease
          description: Sum of decreased credit balance
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]

  - name: mart_historical_balance_by_payment_terms_bucket_monthly
    description: '{{ doc("mart_historical_balance_by_payment_terms_bucket_monthly") }}'
    columns:
        - name: period
          description: Accounting period
          data_tests:
            - not_null
        - name: balance
          description: Open balance in period
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]

  - name: mart_invoices_in_preview_monthly
    description: '{{ doc("mart_invoices_in_preview_monthly") }}'
    columns:
        - name: period
          description: Accounting period
          data_tests:
            - not_null
            - unique
        - name: pending_invoice_amount
          description: Sum of pending amounts
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]
        - name: pending_invoice_count
          description: Count of pending invoices
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ] 

  - name: mart_ar_per_ssp_channel_monthly
    description: '{{ doc("mart_ar_per_ssp_channel_monthly") }}'
    columns:
        - name: period
          description: Accounting period
          data_tests:
            - not_null
        - name: channel
          description: channel type
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]
        - name: total_balance_per_channel
          description: Balance amount per channel
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ] 

  - name: mart_payment_type_monthly
    description: '{{ doc("mart_payment_type_monthly") }}'
    columns:
        - name: period
          description: Accounting period
          data_tests:
            - not_null
        - name: total_payment_amount
          description: payment amount
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]

  - name: mart_current_ar_by_aging_payment_terms
    description: '{{ doc("mart_current_ar_by_aging_payment_terms") }}'
    columns:
        - name: dim_invoice_id
          data_tests:
            - not_null
            - unique
          tags: [ "tdf", "mart", "zuora" ]     
        - name: balance
          data_tests:
            - not_null   

  - name: mart_booking_by_order_type_monthly
    description: '{{ doc("mart_booking_by_order_type_monthly") }}'
    columns:
        - name: period
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ] 
        - name: subscription_type
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]    

  - name: mart_booking_by_payment_schedule_opportunity_category_monthly
    description: '{{ doc("mart_booking_by_payment_schedule_opportunity_category_monthly") }}'
    columns:
        - name: period
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]   
        - name: standard_booking_count
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]   

  - name: mart_collections_monthly
    description: '{{ doc("mart_collections_monthly") }}'
    columns:
        - name: payment_period
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]   
        - name: billed_period
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]    

  - name: mart_cdot_invoices_manual_intervention_monthly
    description: '{{ doc("mart_cdot_invoices_manual_intervention_monthly") }}'
    columns:
        - name: period
          data_tests:
            - not_null
            - unique
          tags: [ "tdf", "mart", "zuora" ]   
        - name: count_all_cdot_invoices
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]

  - name: mart_public_sector_ar_monthly
    description: '{{ doc("mart_public_sector_ar_monthly") }}'
    columns:
        - name: period
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]   
        - name: total_balance_per_segment
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]   

  - name: mart_web_direct_vs_sales_assisted_ar_monthly
    description: '{{ doc("mart_web_direct_vs_sales_assisted_ar_monthly") }}'
    columns:
        - name: period
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]   
        - name: total_balance_per_path
          data_tests:
            - not_null
          tags: [ "tdf", "mart", "zuora" ]   

  - name: mart_booking_billing_ar_monthly
    description: '{{ doc("mart_booking_billing_ar_monthly") }}'
    columns:
        - name: period
          data_tests:
            - not_null
            - unique
          tags: [ "tdf", "mart", "zuora" ]   

