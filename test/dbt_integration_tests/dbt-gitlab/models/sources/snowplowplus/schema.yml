version: 2

models:
    - name: snowplow_fishtown_good_events_source
      description: This is the source table for snowplow events captured by Fishtown's infrastructure.
    
    - name: snowplow_fishtown_good_events_sample_source
      description: This is the source table for a subset of snowplow events captured by Fishtown's infrastructure.
    
    - name: snowplow_fishtown_bad_events_source
      description: This is the source table for bad event data sent to Fishtown's infrastructure.

    - name: snowplow_gitlab_good_events_source
      description: This is the source table for snowplow events captured by GitLab's infrastructure.
    
    - name: snowplow_gitlab_good_events_sample_source
      description: This is the source table for a subset of snowplow events captured by GitLab's infrastructure.

    - name: snowplow_gitlab_bad_events_source
      description: This is the source table for bad event data sent to GitLab's infrastructure.

    - name: snowplow_duplicate_events_source
      description: This is a table that contains the duplicated event ids across the entire data set and is used to remove them from downstream models.
      columns:
          - name: event_id
