version: 2

models:
    - name: pte_scores_source
      description: '{{ doc("pte_scores_source") }}'
    - name: ptc_scores_source
      description: '{{ doc("ptc_scores_source") }}'
    - name: ptpt_scores_source
      description: '{{ doc("ptpt_scores_source") }}'
    - name: namespace_segmentation_scores_source
      description: '{{ doc("ptpt_scores_source") }}'
    - name: ptpf_scores_source
      description: '{{ doc("ptpf_scores_source") }}'
    - name: churn_forecasting_scores_source
      description: '{{ doc("churn_forecasting_scores_source") }}'
    - name: ptpl_scores_source
      description: '{{ doc("ptpl_scores_source") }}'
    - name: opportunity_forecasting_scores_source
      description: '{{ doc("opportunity_forecasting_scores_source") }}'
    - name: icp_successful_accounts_scores_source
      description: '{{ doc("icp_successful_accounts_scores_source") }}'





