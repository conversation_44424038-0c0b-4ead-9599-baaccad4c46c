version: 2

models:
    - name: gitlab_pto_source
      description: '{{ doc("gitlab_pto_source") }}'
      columns:
        - name: end_date
          description: '{{ doc("gitlab_pto_end_date") }}'
          data_tests:
            - not_null
        - name: start_date
          description: '{{ doc("gitlab_pto_start_date") }}'
          data_tests:
            - not_null
        - name: pto_status
          description: '{{ doc("gitlab_pto_pto_status") }}'
          data_tests:
            - not_null
        - name: employee_day_length
          description: '{{ doc("gitlab_pto_employee_day_length") }}'
          data_tests:
            - not_null
        - name: hr_employee_id
          description: '{{ doc("gitlab_pto_hr_employee_id") }}'
          data_tests:
            - not_null
        - name: employee_uuid
          description: '{{ doc("gitlab_pto_employee_uuid") }}'
          data_tests:
            - not_null
        - name: pto_uuid
          description: '{{ doc("gitlab_pto_pto_uuid") }}'
          data_tests:
            - not_null
        - name: pto_date
          description: '{{ doc("gitlab_pto_pto_date") }}'
        - name: pto_ends_at
          description: '{{ doc("gitlab_pto_pto_ends_at") }}'
        - name: is_holiday
          description: '{{ doc("gitlab_pto_is_holiday") }}'
          data_tests:
            - not_null
        - name: recorded_hours
          description: '{{ doc("gitlab_pto_recorded_hours") }}'
          data_tests:
            - not_null
        - name: pto_starts_at
          description: '{{ doc("gitlab_pto_pto_starts_at") }}'
        - name: total_hours
          description: '{{ doc("gitlab_pto_total_hours") }}'
          data_tests:
            - not_null
        - name: pto_group_type
          description: '{{ doc("gitlab_pto_pto_group_type") }}'
        - name: is_pto
          description: '{{ doc("gitlab_pto_is_pto") }}'  
        - name: pto_type_name
          description: '{{ doc("gitlab_pto_pto_type_name") }}'
        - name: pto_type_uuid
          description: '{{ doc("gitlab_pto_pto_type_uuid") }}'
        - name: pto_created_at
          description: '{{ doc("gitlab_pto_pto_created_at") }}'
        