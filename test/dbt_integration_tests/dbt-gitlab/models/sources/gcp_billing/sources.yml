version: 2

sources:
  - name: gcp_billing
    database: '{{ env_var("SNOWFLAKE_LOAD_DATABASE") }}'
    schema: gcp_billing
    loaded_at_field: uploaded_at
    loader: Airflow, gcp_billing
    description: GCP Billing Data from Bigquery [Original Issue](https://gitlab.com/gitlab-data/analytics/-/issues/5268)

    quoting:
      database: false
      schema: false
      identifier: false


    tables:
      - name: detail_gcp_billing
        description: "GCP Billing detail table exported from bigQuery"
        external:
          location: "@raw.gcp_billing.GCP_BILLING_GCS_EXPORT/detail"
          file_format: "( type = parquet )"
          auto_refresh: false
          partitions:
            - name: date_part
              data_type: date
              expression: to_date(split_part(metadata$filename, '/', 2),'YYYY-MM-DD')
       
      - name: summary_gcp_billing
        description: "GCP Billing detail table exported from bigQuery"
        external:
          location: "@raw.gcp_billing.GCP_BILLING_GCS_EXPORT/summary"
          file_format: "( type = parquet )"
          auto_refresh: false
          partitions:
            - name: date_part
              data_type: varchar
              expression: split_part(metadata$filename, '/', 2)
