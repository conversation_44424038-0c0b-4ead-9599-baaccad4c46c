version: 2

sources:
  - name: snowflake
    database: '{{ env_var("SNOWFLAKE_LOAD_DATABASE") }}'
    schema: snowflake
    loader: Airflow
    loaded_at_field: to_timestamp_ntz(_uploaded_at::number)
    
    quoting:
      database: false
      schema: false
      identifier: false

    freshness:
        warn_after: {count: 1, period: day}

    tables:
      - name: grants_to_user
      - name: grants_to_role
      - name: roles
      - name: task_history
        identifier: task_history_view
        freshness: null
      - name: users

  - name: snowflake_account_usage
    database: snowflake
    schema: account_usage

    quoting:
      database: false
      schema: false
      identifier: false
    
    tables:
      - name: access_history
      - name: query_history
      - name: warehouse_metering_history
      - name: tables
      - name: tag_references
      - name: databases
      - name: automatic_clustering_history

