version: 2

models:
  - name: gitlab_dotcom_alert_management_alert_assignees_source
    description: Base model for Gitlab.com alert_management_alert_assignees
    columns:
      - name: alert_management_alert_assignee_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
  - name: gitlab_dotcom_application_settings_source
    description: Base model for Gitlab.com application settings data
    columns:
      - name: application_settings_id
        data_tests:
          - not_null
          - unique
  - name: gitlab_dotcom_approvals_source
    description: Base model for Gitlab.com approvals
    columns:
      - name: approval_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_audit_events_source
    description: Base model for Gitlab.com audit events
    columns:
      - name: audit_event_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: author_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_award_emoji_source
    description: Base model for Gitlab.com award emojis.
    columns:
      - name: award_emoji_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_banned_users_source
    description: Base model for Gitlab.com banned users
    columns:
      - name: user_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_board_assignees_source
    description: Base model for Gitlab.com Board Assignees
    columns:
      - name: board_assignee_relation_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: board_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_board_labels_source
    description: Base model for Gitlab.com board labels
    columns:
      - name: board_label_relation_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: board_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: label_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_boards_source
    description: Base model for Gitlab.com board
    columns:
      - name: board_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_cluster_agents_source
    description: Source data for the kubernetes cluster with agent by projects.
    columns:
      - name: cluster_agent_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_container_repositories_source
    description: Source data for the link between continuer repositories and projects.
    tags: ["gitlab_dotcom"]
    columns:
      - name: container_repository_id
        data_tests:
          - not_null
      - name: project_id
      - name: container_repository_name
        description: This name may be null if the container directly at the project level.
      - name: created_at
      - name: updated_at

  - name: gitlab_dotcom_ci_build_trace_chunks_source
    description: Base model for Gitlab.com ci_build_trace_chunks
    columns:
      - name: ci_build_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_ci_builds_source
    description: Base model for Gitlab.com ci_builds
    columns:
      - name: ci_build_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique:
              config:
                where: "created_at >= DATEADD('day',-3,CURRENT_DATE())"
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
      
  - name: gitlab_dotcom_ci_builds_metadata_source
    description: Base model for Gitlab.com ci_builds
    columns:
      - name: ci_builds_metadata_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_ci_group_variables_source
    description: Base model for Gitlab.com ci_group_variables
    columns:
      - name: ci_group_variable_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_ci_job_artifacts_source
    description: Base model for Gitlab.com ci_job_artifacts
    columns:
      - name: ci_job_artifact_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_ci_pipeline_artifacts_source
    description: Base model for Gitlab.com ci_pipeline_artifacts
    columns:
      - name: ci_pipeline_artifact_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_ci_pipeline_schedule_variables_source
    description: Base model for Gitlab.com ci_pipeline_schedule_variables
    columns:
      - name: ci_pipeline_schedule_variable_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_ci_pipeline_schedules_source
    description: Base model for Gitlab.com ci_pipeline_schedules
    columns:
      - name: ci_pipeline_schedule_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_ci_secure_files
    description: Base model for Gitlab.com CI Secure Files
    columns:
      - name: ci_secure_files_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_ci_subscriptions_projects_source
    description: Base model for Gitlab.com CI subscirptions projects
    columns:
      - name: ci_subscriptions_projects_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_dast_profiles_source
    description: Base model for Gitlab.com dast_profiles
    columns:
      - name: dast_profiles_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_epic_issues_source
    description: Base model for Gitlab.com epic issues
    columns:
      - name: epic_issues_relation_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: epic_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: issue_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_ci_pipelines_source
    description: Base model for Gitlab.com ci_pipelines
    columns:
      - name: ci_pipeline_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_ci_runner_projects_source
    description: Base model for Gitlab.com ci_runner_projects
    columns:
      - name: ci_runner_project_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: runner_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: project_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null


  - name: gitlab_dotcom_ci_runners_source
    description: Base model for Gitlab.com ci_runners
    columns:
      - name: runner_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_ci_sources_pipelines_source
    description: Base model for Gitlab.com ci_sources_pipelines
    columns:
      - name: ci_source_pipeline_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_ci_stages_source
    description: Base model for Gitlab.com ci_stages
    columns:
      - name: ci_stage_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_ci_trigger_requests_source
    description: Base model for Gitlab.com ci_trigger_requests
    columns:
      - name: ci_trigger_request_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_ci_triggers_source
    description: Base model for Gitlab.com ci_triggers
    columns:
      - name: ci_trigger_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_clusters_applications_cert_managers_source
    columns:
      - name: clusters_applications_cert_managers_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: cluster_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_clusters_applications_crossplane_source
    columns:
      - name: clusters_applications_crossplane_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: cluster_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_clusters_applications_elastic_stacks_source
    columns:
      - name: clusters_applications_elastic_stacks_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: cluster_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_clusters_applications_helm_source
    description: Base model for the Gitlab.com `clusters_applications_helm` table.
    columns:
      - name: clusters_applications_helm_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: cluster_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_clusters_applications_ingress_source
    columns:
      - name: clusters_applications_ingress_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: cluster_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_clusters_applications_jupyter_source
    columns:
      - name: clusters_applications_jupyter_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: cluster_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_clusters_applications_knative_source
    columns:
      - name: clusters_applications_knative_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: cluster_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_clusters_applications_prometheus_source
    columns:
      - name: clusters_applications_prometheus_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: cluster_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_clusters_applications_runners_source
    columns:
      - name: clusters_applications_runners_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: cluster_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_clusters_source
    columns:
      - name: cluster_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_cluster_groups_source
    description: Base model for Gitlab.com cluster_groups
    columns:
      - name: cluster_group_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: cluster_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: group_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_cluster_projects_source
    description: Base model for Gitlab.com cluster_projects
    columns:
      - name: cluster_project_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: cluster_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: project_id

  - name: gitlab_dotcom_deployment_merge_requests_source
    description: Base model for GitLab.com deployments_merge_requests.
    columns:
      - name: deployment_merge_request_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - unique
          - not_null
      - name: deployment_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: merge_request_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_deployments_source
    description: Base model for GitLab.com deployments.
    columns:
      - name: deployment_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: deployment_iid
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: project_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: environment_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_elasticsearch_indexed_namespaces_source
    description: Base model for GitLab.com elasticsearch indexed namespaces.
    columns:
      - name: namespace_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: task_instance_name
      - name: uploaded_at

  - name: gitlab_dotcom_environments_source
    description: Base model for GitLab.com environments.
    columns:
      - name: environment_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: project_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_epics_source
    description: Base model for Gitlab.com epics
    columns:
      - name: epic_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: state_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - accepted_values:
              values: [1, 2, 3, 4]
      - name: state
        description: Inferred off of state_id.
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_events_source
    columns:
      - name: event_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_fork_network_members_source
    description: A base model for Gitlab.com fork_network_members.
    columns:
      - name: id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_gpg_signatures_source
    description: Base model for Gitlab.com ssh_signatures
    columns:
      - name: gpg_signatures_id
        description: Unique identifier for gpg_signatures
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        data_tests:
          - not_null
      - name: updated_at
        data_tests:
          - not_null

  - name: gitlab_dotcom_group_audit_events_source
    description: Base model for Gitlab.com ssh_signatures
    columns:
      - name: id
        data_tests:
          - not_null
          - unique
      - name: created_at
        data_tests:
          - not_null

  - name: gitlab_dotcom_identities_source
    description: Base model for GitLab.com identities (3rd party login providers).
    columns:
      - name: identity_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: identity_provider
        description: Which service is the identity provided through? (Google, GitHub, etc.)
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: created_at
      - name: updated_at

  - name: gitlab_dotcom_incident_management_issuable_escalation_statuses_source
    description: Base model for Gitlab.com Issuable Escalation Statuses
    columns:
      - name: status_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_incident_management_timeline_event_tag_links_source
    description: Base model for Gitlab.com Incident Management Timeline Event Tag Links
    columns:
      - name: event_tag_link_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_incident_management_timeline_event_tags_source
    description: Base model for Gitlab.com Incident Management Timeline Event Tags
    columns:
      - name: event_tag_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_incident_management_timeline_events_source
    description: Base model for Gitlab.com Incident Management Timeline Events
    columns:
      - name: id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_issuable_severities_source
    description: Base model for Gitlab.com Incident Issue Severities
    columns:
      - name: issue_severity_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: issue_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: severity
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
  - name: gitlab_dotcom_issue_assignees_source
    description: >-
      Base model for Gitlab.com issue assignees, duplicate rows have not been included.
    columns:
      - name: user_issue_relation_id
        description: unique key created with an md5 hash of user_id and issue_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: user_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: issue_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_issue_metrics_source
    description: Base model for Gitlab.com issue metrics
    columns:
      - name: issue_metric_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: issue_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_issues_source
    description: Base model for Gitlab.com issues
    columns:
      - name: issue_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: state
        description: >-
          The source table has both "state" and "state_id" columns. We use state_id to
          infer the "state" string as we found this gives results that match the API and UI.
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: state_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - accepted_values:
              values: [1, 2, 3, 4]
      - name: duplicated_to_id
      - name: promoted_to_epic_id
      - name: issue_title
        description: '{{ doc("visibility_documentation") }}'
      - name: issue_description
        description: '{{ doc("visibility_documentation") }}'

  - name: gitlab_dotcom_label_priorities_source
    description: Base model for Gitlab.com label priorities
    columns:
      - name: label_priority_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: project_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: label_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_labels_source
    description: >-
      Base model for Gitlab.com labels, the field `title` is hidden as contains
      PII.
    columns:
      - name: label_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_lists_source
    description: Base model for Gitlab.com lists
    columns:
      - name: list_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_merge_request_diffs_source
    description: Base model for Gitlab.com request diffs
    columns:
      - name: merge_request_diff_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: merge_request_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_merge_request_metrics_source
    description: Base model for Gitlab.com merge request metrics
    columns:
      - name: merge_request_metric_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: merge_request_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_merge_requests_source
    description: >-
      Base model for Gitlab.com merge requests, column `merge_params` is hidden
      for privacy concerns.
    columns:
      - name: merge_request_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: is_merge_to_master
        description: >-
          This fields queries if the target branch name is "master" and returns
          a boolean
      - name: merge_request_state
        description: >-
          The source table has both "state" and "state_id" columns. We use state_id to
          infer the "state" string as we found this gives results that match the API and UI.
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: merge_request_state_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - accepted_values:
              values: [1, 2, 3, 4]

  - name: gitlab_dotcom_milestones_source
    description: Base model for Gitlab.com milestones
    columns:
      - name: milestone_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_namespace_details_source
    description: Base model for Gitlab.com namespace details. More information about this model can be seen [HERE](https://gitlab.com/gitlab-org/gitlab/-/issues/375748)
    columns:
      - name: namespace_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_namespace_statistics_source
    description: Base model for Gitlab.com namespace statistics
    columns:
      - name: namespace_statistics_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: namespace_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_namespace_settings_source
    description: Base model for Gitlab.com namespace settings
    columns:
      - name: namespace_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_namespaces_source
    description: Base model for Gitlab.com namespaces
    columns:
      - name: namespace_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: namespace_path
        description: '{{ doc("visibility_documentation") }}'
      - name: namespace_type
        description: '{{ doc("namespace_type") }}'
        data_tests:
          - not_null
          - accepted_values:
              values: ['Group', 'User', 'Project']

      - name: namespace_name
        description: '{{ doc("visibility_documentation") }}'
      - name: visbility_level
        description: '{{ doc("visibility_documentation") }}'
      - name: shared_runners_enabled
        description: Indicates whether or not a namespace has shared_runners_enabled

  - name: gitlab_dotcom_notes_source
    description: >-
      This is a base model for Gitlab.com notes. Notes are comments on Commits,
      Epics, Issues, Merge Requests, Snippets, or Wikis.
    columns:
      - name: note_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_notification_settings_source
    description: Base model for Gitlab.com notification settings
    columns:
      - name: notification_settings_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: user_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_oauth_access_tokens_source
    description: >-
      A base model for Gitlab.com oauth access tokens.
      https://docs.gitlab.com/ee/api/oauth2.html
    columns:
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: oauth_access_token_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_onboarding_progresses_source
    description: A base model for Gitlab.com onboarding_progresses.
    columns:
      - name: onboarding_progress_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf", "gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf", "gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_plans_source
    description: >-
      A base model for Gitlab.com plans.
    columns:
      - name: plan_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: plan_name
        description: The english naming of the plan type (gold, silver, etc.)
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: plan_title
        description: The capitalized english naming of the plan type (Gold, Silver, etc.)
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: plan_is_paid
        description: Whether or not this plan type is considered a paid subscription.
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null


  - name: gitlab_dotcom_pages_domains_source
    description: >-
      A base model for Gitlab.com pages domains.
    columns:
      - name: pages_domain_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_programming_languages_source
    description: >-
      A base model for Gitlab.com Gitlab.com programming_languages.
    columns:
      - name: programming_language_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_project_auto_devops_source
    description: Base model for Gitlab.com auto devops
    columns:
      - name: project_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_project_ci_cd_settings_source
    description: Base model for Gitlab.com auto devops
    columns:
      - name: project_ci_cd_settings_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_project_features_source
    description: Base model for Gitlab.com project features
    columns:
      - name: project_feature_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: project_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_project_mirror_data_source
    description: Base model for Gitlab.com mirror data
    columns:
      - name: project_mirror_data_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: project_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_project_repositories_source
    description: Base model for Gitlab.com
    columns:
      - name: project_repository_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: project_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - unique

  - name: gitlab_dotcom_project_settings_source
    description: Base model for Gitlab.com project settings
    columns:
      - name: project_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
  
  - name: gitlab_dotcom_project_security_settings_source
    description: Base model for Gitlab.com project settings
    columns:
      - name: project_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_project_statistics_source
    description: Base model for Gitlab.com project statistics
    columns:
      - name: project_statistics_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: project_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_projects_source
    description: Base model for Gitlab.com projects
    columns:
      - name: project_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
      - name: has_avatar
        description: >-
          This field will try to find an avatar file, if none is available will
          set this value as False, otherwise True.
      - name: visbility_level
        description: '{{ doc("visibility_documentation") }}'
      - name: project_description
        description: '{{ doc("visibility_documentation") }}'
      - name: project_import_source
        description: '{{ doc("visibility_documentation") }}'
      - name: project_issues_template
        description: '{{ doc("visibility_documentation") }}'
      - name: project_name
        description: '{{ doc("visibility_documentation") }}'
      - name: project_path
        description: '{{ doc("visibility_documentation") }}'
      - name: project_import_url
        description: '{{ doc("visibility_documentation") }}'
      - name: project_merge_requests_template
        description: '{{ doc("visibility_documentation") }}'
      - name: project_description
        description: '{{ doc("visibility_documentation") }}'
  - name: gitlab_dotcom_releases_source
    description: Base model for Gitlab.com releases
    columns:
      - name: release_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: project_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_protected_branch_merge_access_levels_source
    description: A base model for Gitlab.com Gitlab.com protected_branch_merge_access_levels.
    columns:
      - name: protected_branch_merge_access_levels_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_protected_branch_push_access_levels_source
    description: A base model for Gitlab.com Gitlab.com protected_branch_push_access_levels.
    columns:
      - name: protected_branch_push_access_levels_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_repository_languages_source
    description: >-
      A base model for Gitlab.com Gitlab.com repository_languages.
    columns:
      - name: repository_language_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: project_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: programming_language_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_resource_label_events_source
    description: '{{ doc("gitlab_dotcom_resource_label_events_source") }}'
    columns:
      - name: resource_label_event_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: action_type_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_resource_milestone_events_source
    columns:
      - name: resource_milestone_event_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_resource_weight_events_source
    columns:
      - name: resource_weight_event_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_reviews_source
    description: Base model for Gitlab.com reviews
    columns:
      - name: reviews_id
        description: Unique identifier for gpg_signatures
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        data_tests:
          - not_null

  - name: gitlab_dotcom_routes_source
    description: Base model for Gitlab.com paths for namespaces and projects which can be used to link to the actual URLs.
    columns:
      - name: route_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_saml_providers_source
    description: Base model for Gitlab.com gitlab_dotcom_saml_providers
    columns:
      - name: saml_provider_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_integrations_source
    description: Base model for Gitlab.com integrations
    columns:
      - name: integration_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_snippets_source
    description: Base model for Gitlab.com snippets
    columns:
      - name: snippet_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: author_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_ssh_signatures_source
    description: Base model for Gitlab.com ssh_signatures
    columns:
      - name: ssh_signatures_id
        description: Unique identifier for ssh_signatures
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        data_tests:
          - not_null
      - name: updated_at
        data_tests:
          - not_null

  - name: gitlab_dotcom_subscriptions_source
    description: Base model for Gitlab.com subscriptions
    columns:
      - name: subscription_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_subscription_add_ons_source
    description: Base model for Gitlab.com subscriptions_add_ons
    columns:
      - name: id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_subscription_add_on_purchases_source
    description: Base model for Gitlab.com subscriptions_add_ons_purchases
    columns:
      - name: id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_subscription_user_add_on_assignments_source
    description: Base model for Gitlab.com subscriptions_add_ons_user_add_on_assignments
    columns:
      - name: id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
  
  - name: gitlab_dotcom_subscription_user_add_on_assignment_versions_source
    description: Base model for Gitlab.com subscriptions_add_ons_user_add_on_assignments
    columns:
      - name: id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: add_on_name
        data_tests:
          - accepted_values:
              values: ['code_suggestions', 'duo_enterprise', 'product_analytics']

  - name: gitlab_dotcom_system_note_metadata_source
    description: Base model for Gitlab.com system_note_metadata
    columns:
      - name: system_note_metadata_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_todos_source
    description: Base model for Gitlab.com todos
    columns:
      - name: todo_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: user_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_user_custom_attributes_source
    description: Base model for Gitlab.com user custom attributes
    columns:
      - name: user_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: user_custom_key
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: user_custom_value
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_user_details_source
    description: Base model for Gitlab.com user_details
    columns:
      - name: user_id
        tags: ["tdf", "gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_user_preferences_source_non_dedupe
    description: Base model for Gitlab.com user_preferences where no deduplication did take place. The field User_id is NOT unique
    columns:
      - name: user_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_user_preferences_source
    description: Base model for Gitlab.com user_preferences
    columns:
      - name: user_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_users_source
    description: >-
      Base model for Gitlab.com users, the following fields are hidden for
      privacy `current_sign_in_ip`, `last_sign_in_ip`, `unconfirmed_email`,
      `website_url`, `notification_email`, `public_email`, `note`,
      `organization`
    columns:
      - name: user_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: role
        description: This is the string translation of the "role" column stored as an integer in the source database. It is converted using the `user_role_mapping` macro.
      - name: role_id
        description: This is the integer representation of a user's role, as it's stored in the database.
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - accepted_values:
              values: [0, 1, 2, 3, 4, 5, 6, 7, 8, 99]
              
  - name: gitlab_dotcom_vulnerability_identifiers_source
    description: >-
      Base model for Gitlab.com vulnerability_identifiers.
    columns:
      - name: id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: created_at
        data_tests:
          - not_null
      - name: updated_at
        data_tests:
          - not_null
          
  - name: gitlab_dotcom_vulnerability_reads_source
    description: A base model for Gitlab.com vulnerability_reads.
    columns:
      - name: id
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_vulnerability_state_transitions_source
    description: A base model for Gitlab.com vulnerability_state_transitions.
    columns:
      - name: id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_security_scans_source
    description: >-
      Base model for Gitlab.com security scans.
    columns:
      - name: security_scan_id
        data_tests:
          - not_null
          - unique
      - name: created_at
      - name: updated_at
      - name: build_id
      - name: scan_type
      - name: project_id
      - name: pipeline_id
      - name: is_latest
      - name: security_scan_status

  - name: gitlab_dotcom_merge_request_predictions_source
    description: >-
      Base model for Gitlab.com suggested merge request reviewers.
    columns:
      - name: merge_request_id
        data_tests:
          - not_null
          - unique
      - name: created_at
      - name: updated_at
      - name: suggested_reviewers
      - name: suggested_reviewers_top_n
      - name: suggested_reviewers_version
      - name: suggested_reviewers_list

  - name: gitlab_dotcom_web_hooks_source
    description: Base model for Gitlab.com WebHooks table. More information about Gitlab webhooks [HERE](https://docs.gitlab.com/ee/user/project/integrations/webhooks.html).
    columns:
      - name: web_hook_id
        data_tests:
          - not_null
          - unique

  - name: gitlab_dotcom_ml_candidates_source
    description: Base model for Gitlab.com ml_candidates table.
    columns:
      - name: ml_candidate_id
        description: Appears as `id` in the source data
        data_tests:
          - not_null
          - unique
      - name: created_at
        data_tests:
          - not_null
      - name: updated_at
        data_tests:
          - not_null
      - name: experiment_id
        data_tests:
          - not_null
      - name: user_id
      - name: start_at
        description: Appears as `start_time` in the source data
      - name: end_at
        description: Appears as `end_time` in the source data
      - name: status
        data_tests:
          - not_null
      - name: package_id
      - name: ml_candidate_eid
        description: Appears as `eid` in the source data
      - name: project_id
      - name: ml_candidate_internal_id
        description: Appears as `internal_id` in the source data
      - name: ci_build_id

  - name: gitlab_dotcom_ml_experiments_source
    description: Base model for Gitlab.com ml_experiments table.
    columns:
      - name: ml_experiment_id
        description: Appears as `id` in the source data
        data_tests:
          - not_null
          - unique
      - name: created_at
        data_tests:
          - not_null
      - name: updated_at
        data_tests:
          - not_null
      - name: ml_experiment_iid
        description: Appears as `iid` in the source data
        data_tests:
          - not_null
      - name: project_id
        data_tests:
          - not_null
      - name: user_id
      - name: deleted_on

  - name: gitlab_dotcom_zoekt_indices_source
    description: Base model for Gitlab.com zoekt indices source.
    columns:
      - name: id
        data_tests:
          - not_null
          - unique
      - name: zoekt_enabled_namespace_id
      - name: zoekt_node_id
        data_tests:
          - not_null
      - name: namespace_id
        data_tests:
          - not_null
      - name: state
        data_tests:
          - not_null
      - name: created_at
        data_tests:
          - not_null
      - name: updated_at
        data_tests:
          - not_null
  - name: gitlab_dotcom_zoekt_enabled_namespaces_source
    description: Base model for Gitlab.com zoekt indices source.
    columns:
      - name: id
        data_tests:
          - not_null
          - unique
      - name: root_namespace_id
        data_tests:
          - not_null
      - name: search
        data_tests:
          - not_null
      - name: created_at
        data_tests:
          - not_null
      - name: updated_at
        data_tests:
          - not_null
