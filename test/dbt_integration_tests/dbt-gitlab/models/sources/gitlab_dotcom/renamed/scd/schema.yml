version: 2

models:
  - name: gitlab_dotcom_issue_links_source
    description: 'Base model for Gitlab.com issue links. {{ doc("scd_type_two_documentation") }}'
    columns:
      - name: issue_link_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique:
              where: "valid_to = NULL"
      - name: created_at
      - name: updated_at
      - name: valid_from
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: valid_to
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - dbt_utils.expression_is_true:
              expression: ">= valid_from"

  - name: gitlab_dotcom_gitlab_subscriptions_source
    description: '{{ doc("gitlab_dotcom_gitlab_subscriptions_source") }} {{ doc("scd_type_two_documentation") }}'
    columns:
      - name: gitlab_subscription_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: namespace_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique:
              where: "valid_to = NULL"
      - name: gitlab_subscription_trial_starts_on
        description: The date that the subscription started their trial.
      - name: gitlab_subscription_trial_ends_on
        description: The date that the subscription's trial ended or is scheduled to end (can be in the future.) Should always be populated when gitlab_subscription_trial_starts_on is populated.
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: valid_from
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: valid_to
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - dbt_utils.expression_is_true:
              expression: ">= valid_from"

  - name: gitlab_dotcom_group_group_links_source
    description: This table represents groups being shared with groups. {{ doc("scd_type_two_documentation") }}'
    tags: ["tdf","gitlab_dotcom"]
    data_tests:
      - dbt_utils.expression_is_true:
          expression: "valid_from <= valid_to"
    columns:
      - name: group_group_link_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: shared_group_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: shared_with_group_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null

  - name: gitlab_dotcom_label_links_source
    description: 'Base model for Gitlab.com label links. {{ doc("scd_type_two_documentation") }}'
    columns:
      - name: label_link_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique:
              where: "valid_to = NULL"
      - name: valid_from
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: valid_to
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - dbt_utils.expression_is_true:
              expression: ">= valid_from"


  - name: gitlab_dotcom_merge_request_reviewers_source
    description: 'Base model for Gitlab.com members. {{ doc("scd_type_two_documentation") }}'
    columns:
      - name: merge_request_reviewer_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique:
              where: "valid_to = NULL"
      - name: valid_from
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: valid_to
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - dbt_utils.expression_is_true:
              expression: ">= valid_from"

  - name: gitlab_dotcom_members_source
    description: 'Base model for Gitlab.com members. {{ doc("scd_type_two_documentation") }}'
    columns:
      - name: member_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique:
              where: "valid_to = NULL"
      - name: valid_from
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: valid_to
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - dbt_utils.expression_is_true:
              expression: ">= valid_from"

  - name: gitlab_dotcom_project_group_links_source
    description: 'Base model for Gitlab.com group links. {{ doc("scd_type_two_documentation") }}'
    columns:
      - name: project_group_link_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
          - unique:
              where: "valid_to = NULL"
      - name: project_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: group_id
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: created_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: updated_at
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: valid_from
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - not_null
      - name: valid_to
        tags: ["tdf","gitlab_dotcom"]
        data_tests:
          - dbt_utils.expression_is_true:
              expression: ">= valid_from"

  - name: gitlab_dotcom_merge_request_diff_commit_users_source
    description: 'Base model for Gitlab.com group links. {{ doc("scd_type_two_documentation") }}'
    columns:
      - name: id
        tags: ["gitlab_dotcom"]
        data_tests:
          - not_null
      - name: name
        tags: ["gitlab_dotcom"]
      - name: email
        tags: ["gitlab_dotcom"]
