version: 2

sources:
  - name: zuora
    tags: ["tdf","zuora"]
    database: '{{ env_var("SNOWFLAKE_LOAD_DATABASE") }}'
    schema: zuora_stitch_rest_api
    loader: Stitch
    loaded_at_field: _sdc_batched_at

    quoting:
      database: false
      schema: false
      identifier: false

    freshness:
      warn_after: {count: 8, period: hour}
      error_after: {count: 24, period: hour}

    tables:
      - name: order
        identifier: ORDER
        quoting:
          identifier: true
        description: '{{ doc("zuora_order_source") }}'
        columns:
          - name: id
            description: Primary Key for orders
            data_tests:
              - dbt_utils.expression_is_true:
                  expression: "REGEXP '[0-9a-z]{32}'"
      - name: order_action
        identifier: orderaction
        description: '{{ doc("zuora_order_action_source") }}'
        columns:
          - name: id
            description: Primary Key for order actions
            data_tests:
              - dbt_utils.expression_is_true:
                  expression: "REGEXP '[0-9a-z]{32}'"
