version: 2

models:
    - name: google_analytics_4_events_source
      columns:
        - name: date_part
          data_tests:
            - not_null
        - name: geo_city# 
#           meta:
#             masking_policy: analyst_marketing_sensitive
        - name: geo_continent# 
#           meta:
#             masking_policy: analyst_marketing_sensitive
        - name: geo_country# 
#           meta:
#             masking_policy: analyst_marketing_sensitive
        - name: geo_metro# 
#           meta:
#             masking_policy: analyst_marketing_sensitive
        - name: geo_region# 
#           meta:
#             masking_policy: analyst_marketing_sensitive
        - name: geo_sub_continent# 
#           meta:
#             masking_policy: analyst_marketing_sensitive
    - name: google_analytics_4_pseudonymous_users_source
      columns:
        - name: pseudo_user_id
          data_tests:
            - not_null
        - name: date_part
          data_tests:
            - not_null
        - name: geo_city# 
#           meta:
#             masking_policy: analyst_marketing_sensitive
        - name: geo_continent# 
#           meta:
#             masking_policy: analyst_marketing_sensitive
        - name: geo_country# 
#           meta:
#             masking_policy: analyst_marketing_sensitive
        - name: geo_region# 
#           meta:
#             masking_policy: analyst_marketing_sensitive
    - name: google_analytics_4_user_audiences_source
      columns:
        - name: date_part
          data_tests:
            - not_null
