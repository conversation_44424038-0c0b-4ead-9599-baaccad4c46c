version: 2

sources:
  - name: servicenow_prd
    database: RAW
    schema: servicenow_prd
    loaded_at_field: _fivetran_synced
    loader: fivetran
    description: Servicenow data [Original Issue](https://gitlab.com/gitlab-data/analytics/-/issues/22795)

    quoting:
      database: false
      schema: false
      identifier: false

    tables:
      - name: incident
      - name: incident_task
      - name: problem
      - name: problem_task
      - name: sla
      - name: sys_user
      - name: sys_user_group
      - name: task