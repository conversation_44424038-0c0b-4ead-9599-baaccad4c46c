version: 2

models:
  - name: bamboohr_compensation_source
    description: A log of compensation and compensation related updates. This model is temporary and is used for downstream models.
    columns:
      - name: compensation_update_id
      - name: employee_id
        description: Foreign Key to employee info
      - name: effective_date
        description: Date the compensation change goes into effect
      - name: compensation_type
      - name: compensation_change_reason
        description: Compensation can change because of promotions, role changes, annual reviews and for other reasons.
      - name: pay_rate
      - name: compensation_value# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: compensation_currency
      - name: uploaded_at    

  - name: bamboohr_currency_conversion_source
    description: This table tells us what the conversion is for local salary to USD.  Raw data format changed on 2022-04-22.
    columns:
      - name: conversion_id
        data_tests:
          - unique
          - not_null
      - name: employee_id 
        description: Foreign Key to employee info
        data_tests:
          - not_null
      - name: effective_date
      - name: currency_conversion_factor
      - name: local_annual_salary_amount
      - name: local_currency_code
      - name: usd_annual_salary_amount
      - name: prior_bamboohr_annual_salary     

  - name: bamboohr_custom_bonus_source
    description: There are multiple bonuses that a team member can earn that are captured here. Raw JSON format changed on 2022-04-22.
    columns:
      - name: bonus_id
        data_tests:
            - unique
            - not_null
      - name: employee_id
        description: Foreign Key to employee info
        data_tests:
            - not_null
      - name: bonus_type
        data_tests:
          - not_null

  - name: bamboohr_directory_source
    description: Base model for BambooHR Active Directory. Brings in directory data for each day.
    columns:
      - name: work_email
        tags: ["tdf"]
        data_tests:
            - dbt_utils.expression_is_true:
                expression: "ILIKE '%@%.%'"
  - name: bamboohr_emergency_contacts_source
    description: Bamboohr emergency contacts table. Used to create an alert in bamboohr_missing_emergency_contact_alert to identify individuals we do not have a contact for.
    columns:
      - name: employee_id
      - name: full_name# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: home_phone# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: mobile_phone# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: work_phone# 
#         meta:
#           masking_policy: analyst_people_sensitive

  - name: bamboohr_employment_status_source
    description: This model shows changes in employment status (active, terminated, on leave, etc). Updates are captured in log-style.
    columns:
      - name: status_id
        data_tests:
            - unique
            - not_null
      - name: employee_id
        data_tests:
            - not_null
      - name: termination_type
        data_tests:
          - accepted_values:
              values: ['Termination (Involuntary)', 'Resignation (Voluntary)', 'Death']# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: employment_status
            
  - name: bamboohr_id_employee_number_mapping_source
    description: This model is based off the custom report in bamboohr to capture additional bamboohr information. This captures a snapshot of custom fields in bamboohr everyday. Note - field names can be added at various times through this report depending on use case and it is important to understand when we started capturing these data points.
    data_tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - employee_number
            - uploaded_at
    columns:
      - name: employee_id
      - name: employee_number
      - name: greenhouse_candidate_id
        description: Captures the id associated to the employee in Greenhouse (our recruiting platform). This will only be applicable in Greenhouse starting 2019-12-13, and will help identify which candidates were hired.
      - name: jobtitle_speciality_multi_select
        description: Began collecting this filed 2021-12-16  
      - name: jobtitle_speciality_single_select
        description: Started capturing on 2020-05-07
      - name: nationality# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: gender_dropdown# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: locality# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: age# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: country# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: ethnicity# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: gender# 
#         meta:
#           masking_policy: analyst_people_sensitive
          
  - name: bamboohr_job_info_source
    description: This model shows the details (manager, department, and division) that an employee is in at a given point in time. This includes historical information.
    columns:
      - name: job_id
        data_tests:
            - unique
            - not_null
      - name: employee_id
        data_tests:
            - not_null
      - name: job_title
        data_tests:
            - not_null
      - name: effective_date
        data_tests:
            - not_null
      - name: department
      - name: division
      - name: entity
        data_tests:
            - not_null
      - name: reports_to
        data_tests:
            - not_null
      - name: job_role      

  - name: bamboohr_ote_source
    description: Provides on-target-earning as related to sales roles.  Raw data format changes on 2022-04-22.
    columns:
      - name: target_earnings_update_id
        data_tests:
          - unique
          - not_null
      - name: employee_id
        description: Foreign Key to employee info
        data_tests:
          - not_null
      - name: effective_date
      - name: variable_pay# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: annual_amount_local# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: annual_amount_usd_value# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: ote_local# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: ote_usd# 
#         meta:
#           masking_policy: analyst_people_sensitive
      - name: ote_type# 
#         meta:
#           masking_policy: analyst_people_sensitive
 
