version: 2

sources:
  - name: zendesk
    database: '{{ env_var("SNOWFLAKE_LOAD_DATABASE") }}'
    schema: tap_zendesk
    loader: <PERSON><PERSON>
    loaded_at_field: __loaded_at

    quoting:
      database: false
      schema: false
      identifier: false

    freshness:
      warn_after: {count: 8, period: hour}
      error_after: {count: 24, period: hour}

    tables:
      - name: groups
        description: '{{ doc("zendesk_groups_desc") }}'
      - name: group_memberships
        description: '{{ doc("zendesk_group_memberships_desc") }}'
      - name: macros
        description: '{{ doc("zendesk_macros_desc") }}'
      - name: organizations
        description: '{{ doc("zendesk_org_desc") }}'
      - name: satisfaction_ratings
        description: '{{ doc("zendesk_satisfaction_ratings_desc") }}'
      - name: sla_policies
        description: '{{ doc("zendesk_sla_policies_desc") }}'
      - name: tags
        description: '{{ doc("zendesk_tags_desc") }}'
      - name: tickets
        description: '{{ doc("zendesk_tickets_desc") }}'
      - name: ticket_audits
        description: '{{ doc("zendesk_ticket_audits_desc") }}'
      - name: ticket_fields
        description: '{{ doc("zendesk_ticket_fields_desc") }}'
      - name: ticket_forms
        description: '{{ doc("zendesk_ticket_forms_desc") }}'
      - name: ticket_comments
        description: '{{ doc("zendesk_ticket_comments_desc") }}'
      - name: ticket_metrics
        description: '{{ doc("zendesk_ticket_metrics_desc") }}'
      - name: users
        description: '{{ doc("zendesk_users_desc") }}'
