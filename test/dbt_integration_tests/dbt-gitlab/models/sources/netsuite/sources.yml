version: 2

sources:
  - name: netsuite
    database: '{{ env_var("SNOWFLAKE_LOAD_DATABASE") }}'
    schema: netsuite_fivetran
    loader: fivetran
    loaded_at_field: _FIVETRAN_SYNCED

    quoting:
      database: false
      schema: false
      identifier: false

    freshness:
      warn_after: {count: 8, period: hour}
      error_after: {count: 24, period: hour}

    tables:
      - name: accounting_books
      - name: accounting_periods
      - name: accounts
      - name: budget
      - name: budget_category
      - name: classes
      - name: consolidated_exchange_rates
      - name: currency_exchange_rates
      - name: currencies
      - name: customers
      - name: departments
      - name: entity
      - name: posting_account_activity
      - name: subsidiaries
      - name: transaction_lines
        columns:
          - name: transaction_id
            data_tests:
              - relationships:
                  to: source('netsuite', 'transactions')
                  field: transaction_id
                  severity: warn
      - name: transactions
      - name: vendors
