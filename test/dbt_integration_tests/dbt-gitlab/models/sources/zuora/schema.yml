version: 2

models:
  - name: zuora_account_source
    description: Source layer for Zuora Accounts for cleaning and renaming
  - name: zuora_accounting_period_source
    description: Source layer for Zuora Accounting Periods for cleaning and renaming
    columns:
      - name: accounting_period_id
        tags: ["tdf", "zuora"]
        data_tests:
          - not_null
      - name: accounting_period_start_date
        tags: ["tdf", "zuora"]
        data_tests:
          - not_null
  - name: zuora_amendment_source
    description: Source layer for Zuora Amendments for cleaning and renaming
    columns:
      - name: amendment_id
        tags: ["tdf", "zuora"]
        data_tests:
          - not_null
          - unique
  - name: zuora_booking_transaction_source
    description: Source layer for Zuora Booking Transactions for renaming
  - name: zuora_contact_source
    description: Source layer for Zuora Contacts for cleaning and renaming
  - name: zuora_credit_balance_adjustment_source
    description: Source layer for Zuora Contacts for renaming
  - name: zuora_discount_applied_metrics_source
    description: Source layer for Zuora Discount Applied Metrics  for cleaning and renaming
  - name: zuora_invoice_source
    description: Source layer for Zuora Invoices for cleaning and renaming
  - name: zuora_invoice_item_source
    description: Source layer for Zuora Invoice Items for cleaning and renaming
  - name: zuora_invoice_item_adjustment_source
    description: Source layer for Zuora Contacts for cleaning and renaming
  - name: zuora_invoice_payment_source
    description: Source layer for Zuora Products for cleaning and renaming
    columns:
      - name: invoice_payment_id
        tags: ["tdf", "zuora"]      
        data_tests:
          - not_null
          - unique
      - name: invoice_id
        tags: ["tdf", "zuora"]      
        data_tests:
          - not_null
  - name: zuora_payment_method_source
    description: Source layer for Zuora Payment Method
    columns:
      - name: payment_method_id
        tags: ["tdf", "zuora"]
        data_tests:
          - not_null
          - unique
  - name: zuora_payment_source
    description: Source layer for Zuora Payment for renaming
  - name: zuora_product_rate_plan_charge_source
    description: Source layer for Zuora Product Rate Plan Charges
  - name: zuora_product_rate_plan_charge_tier_source
    description: Source layer for Zuora Product Rate Plan Charge Tier
  - name: zuora_product_rate_plan_source
    description: Source layer for Zuora Product Rate Plans for cleaning and renaming
  - name: zuora_product_source
    description: Source layer for Zuora Products for cleaning and renaming
    columns:
      - name: product_id
        tags: ["tdf", "zuora"]
        data_tests:
          - not_null
      - name: product_name
        tags: ["tdf", "zuora"]
        data_tests:
          - not_null
  - name: zuora_rate_plan_source
    description: Source layer for Zuora Rate Plans for cleaning and renaming
  - name: zuora_rate_plan_charge_source
    description: Source layer for Zuora Rate Plan Charges for cleaning and renaming
  - name: zuora_rate_plan_charge_tier_source
    description: Source layer for Zuora Rate Plan Charge Tier
  - name: zuora_revenue_schedule_item_source
    description: Source layer for Zuora Revenue Schedule Item for cleaning and renaming
    columns:
      - name: revenue_schedule_item_id
        tags: ["tdf", "zuora"]
        data_tests:
          - not_null
      - name: rate_plan_charge_id
        tags: ["tdf", "zuora"]
        data_tests:
          - not_null
      - name: accounting_period_id
        tags: ["tdf", "zuora"]
        data_tests:
          - not_null
  - name: zuora_refund_source
    description: Source layer for Zuora Refunds for cleaning and renaming
  - name: zuora_refund_invoice_payment_source
    description: Source layer for Zuora Refunds for renaming
  - name: zuora_subscription_source
    description: Source layer for Zuora Subscriptions for cleaning and renaming
