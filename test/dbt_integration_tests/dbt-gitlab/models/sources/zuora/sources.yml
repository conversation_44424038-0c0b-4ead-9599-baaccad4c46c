version: 2

sources:
  - name: zuora
    tags: ["tdf","zuora"]
    database: '{{ env_var("SNOWFLAKE_LOAD_DATABASE") }}'
    schema: zuora_stitch
    loader: Stitch
    loaded_at_field: _sdc_batched_at

    quoting:
      database: false
      schema: false
      identifier: false

    freshness:
      warn_after: {count: 8, period: hour}
      error_after: {count: 24, period: hour}

    tables:
      - name: account
        description: '{{ doc("zuora_account_source") }}'
        columns:
          - name: id
            description: Primary Key for Accounts
            data_tests:
              - dbt_utils.expression_is_true:
                  expression: "REGEXP '[0-9a-z]{32}'"
      - name: accounting_period
        identifier: accountingperiod
        description: '{{ doc("zuora_accounting_period_source") }}'
      - name: amendment
      - name: contact 
        description: '{{ doc("zuora_contact_source") }}'
      - name: credit_balance_adjustment
        identifier: creditbalanceadjustment
        description: '{{ doc("zuora_credit_balance_adjustment_source") }}'
      - name: discount_applied_metrics
        identifier: discountappliedmetrics
        description: '{{ doc("zuora_discount_applied_metrics_source") }}'
        freshness: null
      - name: invoice_item
        identifier: invoiceitem
        description: '{{ doc("zuora_invoice_item_source") }}'
      - name: invoice_item_adjustment
        identifier: invoiceitemadjustment
        description: '{{ doc("zuora_invoice_item_adjustment_source") }}'
      - name: invoice
        description: '{{ doc("zuora_invoice_source") }}'
      - name: invoice_payment
        identifier: invoicepayment
        description: '{{ doc("zuora_invoice_payment_source") }}'
      - name: payment
        identifier: payment
        description: '{{ doc("zuora_payment_source") }}'
      - name: payment_method
        identifier: paymentmethod
        description: '{{ doc("zuora_payment_method_source") }}'
      - name: product
        description: '{{ doc("zuora_product_source") }}'
      - name: product_rate_plan
        identifier: productrateplan
      - name: product_rate_plan_charge
        identifier: productrateplancharge
      - name: product_rate_plan_charge_tier
        identifier: productrateplanchargetier
        freshness: null
      - name: rate_plan_charge
        identifier: rateplancharge
        description: '{{ doc("zuora_rateplan_charge_source") }}'
      - name: rate_plan_charge_tier
        identifier: rateplanchargetier
      - name: rate_plan
        identifier: rateplan
        description: '{{ doc("zuora_rateplan_source") }}'
      - name: refund
        identifier: refund
        description: '{{ doc("zuora_refund_source") }}'
      - name: refund_invoice_payment
        identifier: refundinvoicepayment
        description: '{{ doc("zuora_refund_invoice_payment_source") }}'  
      - name: revenue_schedule_item
        identifier: revenuescheduleitem
        description: '{{ doc("zuora_revenue_schedule_item_source") }}'
      - name: subscription
        description: '{{ doc("zuora_subscription_source") }}'
        columns:
          - name: accountid
            description: Primary Key for Accounts
            data_tests:
              - dbt_utils.expression_is_true:
                  expression: "REGEXP '[0-9a-z]{32}'"
              - relationships:
                  to: source('zuora', 'account')
                  field: id
                  severity: warn
      - name: booking_transaction
        identifier: bookingtransaction
        description: '{{ doc("zuora_booking_transaction_source") }}'
