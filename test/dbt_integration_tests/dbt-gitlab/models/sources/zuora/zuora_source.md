{% docs zuora_account_source %}

The account source table contains information about the customer accounts in your Zuora instance. [Link to Documentation](https://www.stitchdata.com/docs/integrations/saas/zuora#account)

{% enddocs %}

{% docs zuora_accounting_period_source %}

This is the source table for the accounting period table. [Link to Documentation](https://knowledgecenter.zuora.com/Developer_Platform/API/G_SOAP_API/E1_SOAP_API_Object_Reference/AccountingPeriod)

{% enddocs %}

{% docs zuora_booking_transaction_source %}

This is the source table for the booking transactions. [Link to Documentation](https://knowledgecenter.zuora.com/Zuora_Billing/Enable_Order_to_Revenue/Booking_Transactions/AA_Overview_of_Booking_Transaction)

{% enddocs %}

{% docs zuora_contact_source %}

The contact source table contains info about an account’s point-of-contact. [Link to Documentation](https://www.stitchdata.com/docs/integrations/saas/zuora#contact)

{% enddocs %}

{% docs zuora_credit_balance_adjustment_source %}

This table contains info about the credit balance adjustments made on the invoice or account. [Link to Documentation](https://knowledgecenter.zuora.com/Zuora_Central_Platform/API/G_SOAP_API/E1_SOAP_API_Object_Reference/CreditBalanceAdjustment)

{% enddocs %}

{% docs zuora_discount_applied_metrics_source %}

The discountAppliedMetrics source table contains info about rate plan charges that use either a discount-fixed amount or discount-percentage charge model. [Link to Documentation](https://www.stitchdata.com/docs/integrations/saas/zuora#discountappliedmetrics)

{% enddocs %}

{% docs zuora_invoice_item_source %}

The invoiceItem source table contains info about the line items in invoices. [Link to Documentation](https://www.stitchdata.com/docs/integrations/saas/zuora#invoiceitem)

{% enddocs %}

{% docs zuora_invoice_source %}

The invoice source table contains info about invoices, which are bills to customers. [Link to Documentation](https://www.stitchdata.com/docs/integrations/saas/zuora#invoice)

{% enddocs %}

{% docs zuora_invoice_item_adjustment_source %}

The invoice item adjustment source table contains info about the invoice item adjustments made on the invoice. [Link to Documentation](https://knowledgecenter.zuora.com/Zuora_Central_Platform/API/G_SOAP_API/E1_SOAP_API_Object_Reference/InvoiceItemAdjustment)

{% enddocs %}

{% docs zuora_invoice_payment_source %}

The invoice payment source table contains info about payments made on invoices. [Link to Documentation](https://knowledgecenter.zuora.com/Developer_Platform/API/G_SOAP_API/E1_SOAP_API_Object_Reference/InvoicePayment)

{% enddocs %}

{% docs zuora_payment_source %}

The payment source table contains info about payments received into Zuora. The payments can be applied to the invoices or to the account as credit [Link to Documentation](https://knowledgecenter.zuora.com/Zuora_Central_Platform/API/G_SOAP_API/E1_SOAP_API_Object_Reference/Payment_object)

{% enddocs %}

{% docs zuora_payment_method_source %}

The payment method source table contains info about payment method details associated with a customer account. [Link to Documentation](https://knowledgecenter.zuora.com/Central_Platform/API/G_SOAP_API/E1_SOAP_API_Object_Reference/PaymentMethod)

{% enddocs %}

{% docs zuora_product_source %}

This is the source table for the product table. [Link to Documentation](https://knowledgecenter.zuora.com/Developer_Platform/API/G_SOAP_API/E1_SOAP_API_Object_Reference/Product)

{% enddocs %}

{% docs zuora_rateplan_source %}

The ratePlan source table contains info about rate plans, which is a price or collection of prices for services. [Link to Documentation](https://www.stitchdata.com/docs/integrations/saas/zuora#rateplan)

{% enddocs %}

{% docs zuora_rateplan_charge_source %}

This is the source table for Zuora Rate Plan Charges. [Link to Documentation](https://knowledgecenter.zuora.com/DC_Developers/G_SOAP_API/E1_SOAP_API_Object_Reference/RatePlanCharge)

{% enddocs %}

{% docs zuora_refund_source %}

This is the source table for Zuora Refunds. [Link to Documentation](https://knowledgecenter.zuora.com/DC_Developers/G_SOAP_API/E1_SOAP_API_Object_Reference/Refund)

{% enddocs %}

{% docs zuora_refund_invoice_payment_source %}

This is the source table for Zuora Refund Invoice Payments. [Link to Documentation](https://knowledgecenter.zuora.com/Zuora_Central_Platform/API/G_SOAP_API/E1_SOAP_API_Object_Reference/RefundInvoicePayment)

{% enddocs %}

{% docs zuora_revenue_schedule_item_source %}

This is the source table for the Zuora Revenue Schedule Item [Link to Documentation](https://knowledgecenter.zuora.com/Zuora_Central/Reporting_and_Analytics/D_Data_Sources_and_Exports/C_Data_Source_Reference/Revenue_Schedule_Item_Data_Source)

{% enddocs %}

{% docs zuora_subscription_source %}

The subscription source table contains info about your products and/or services with recurring charges.
[Link to Documentation](https://www.stitchdata.com/docs/integrations/saas/zuora#subscription)

{% enddocs %}
