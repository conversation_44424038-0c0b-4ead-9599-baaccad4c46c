version: 2

sources:
  - name: driveload
    database: '{{ env_var("SNOWFLAKE_LOAD_DATABASE") }}'
    schema: driveload
    loaded_at_field: DATEADD(sec, _updated_at, '1970-01-01')
    loader: Python [Driveload](https://gitlab.com/gitlab-data/analytics/tree/master/extract/driveload)
    description: script that loads csvs from Google Drive into Snowflake

    quoting:
      database: false
      schema: false
      identifier: false

    tables:
      - name: clari_export_forecast_net_iacv
      - name: gdpr_delete_requests
      - name: email_domain_classification
      - name: financial_metrics_program_phase_1
      - name: lam_corrections
      - name: marketing_dnc_list
      - name: ssa_coverage_fitted_curves
      - name: ssa_quarterly_aggregated_metrics_for_coverage
      - name: zuora_revenue_billing_waterfall_report
      - name: zuora_revenue_rc_rollforward_report
      - name: zuora_revenue_unreleased_pob_report
      - name: zuora_revenue_unbill_rollforward_report
      - name: zuora_revenue_revenue_waterfall_report
      - name: zuora_revenue_waterfall_report_with_wf_type_adjustments
      - name: zuora_revenue_waterfall_report_with_wf_type_unbilled_revenue
      - name: zuora_revenue_waterfall_report_with_wf_type_net_revenue_with_mje_flag_y
      - name: zuora_revenue_billing_waterfall_report_with_additional_columns
      - name: marketing_press_sov
      - name: booking_to_billing_monthly_reconciliation
      - name: pending_invoices_report
      - name: invoice_aging_detail
