version: 2
models:
  - name: sfdc_sandbox_user_roles_source
    description: Source model for SFDC User Roles
    columns:
      - name: user_role_id
        data_tests:
          - not_null
          - unique
  - name: sfdc_sandbox_account_source
    columns:
      - name: account_id
        data_tests:
          - not_null
          - unique
      - name: account_name
        data_tests:
          - not_null
  - name: sfdc_sandbox_opportunity_source
    description: Source model for SFDC Opportunities
    columns:
      - name: account_id
        data_tests:
          - not_null
      - name: opportunity_id
        data_tests:
          - not_null
          - unique
      - name: opportunity_name
        data_tests:
          - not_null
  - name: sfdc_sandbox_users_source
    columns:
      - name: user_id
        tags: ["tdf", "sfdc"]
        data_tests:
          - not_null
          - unique