version: 2

models:
  - name: zuora_query_api_users_source
    description: Source layer for Zuora Users
    columns:
      - name: zuora_user_id
        tags: ["tdf", "zuora", "zuora_query_api"]
        data_tests:
          - not_null
          - unique

  - name: zuora_query_api_order_action_rate_plan_source
    description: Source layer for Zuora Order Action Rate Plan
    columns:
      - name: order_action_rate_plan_id
        tags: ["tdf", "zuora", "zuora_query_api"]
        data_tests:
          - not_null
          - unique

  - name: zuora_query_api_ramp_interval_source
    description: Source layer for Zuora Ramp intervals
    columns:
      - name: ramp_interval_id
        tags: [ "tdf", "zuora", "zuora_query_api" ]
        data_tests:
          - not_null
          - unique


  - name: zuora_query_api_ramps_source
    description: Source layer for Zuora ramps
    columns:
      - name: ramp_id
        tags: [ "tdf", "zuora", "zuora_query_api" ]
        data_tests:
          - not_null
          - unique

  - name: zuora_query_api_ramp_interval_metrics_source
    description: Source layer for Zuora ramp interval metrics
    columns:
      - name: ramp_interval_metric_id
        tags: [ "tdf", "zuora", "zuora_query_api" ]
        data_tests:
          - not_null
          - unique

  - name: zuora_query_api_charge_contractual_value_source
    description: Source layer for charge_contractual_value
    columns:
      - name: charge_contractual_value_id
        tags: [ "tdf", "zuora", "zuora_query_api" ]
        data_tests:
          - not_null
          - unique

  - name: zuora_query_api_charge_metrics_discount_allocation_detail_source
    description: Source layer for Zuora charge_metrics_discount_allocation_detail
    columns:
      - name: charge_metrics_discount_allocation_detail_id
        tags: [ "tdf", "zuora", "zuora_query_api" ]
        data_tests:
          - not_null
          - unique

  - name: zuora_query_api_charge_metrics_source
    description: Source layer for Zuora charge metrics
    columns:
      - name: charge_metrics_id
        tags: [ "tdf", "zuora", "zuora_query_api" ]
        data_tests:
          - not_null
          - unique