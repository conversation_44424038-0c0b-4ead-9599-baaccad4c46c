version: 2

models:
  - name: categories_yaml_source
    description: Emphemeral layer for extracting and cleaning data from [categories.yml file](https://gitlab.com/gitlab-com/www-gitlab-com/blob/master/data/categories.yml) in the GitLab Handbook.

  - name: categories_yaml_acquisitions_source
    description: Source layer for extracting and cleaning specifically acquisition data from [categories.yml file](https://gitlab.com/gitlab-com/www-gitlab-com/blob/master/data/categories.yml).
 
  - name: content_keystone_source
    description: Content Marketing's keystone file of content attributes. 
    columns:
      - name: content_name
      - name: gitlab_epic
      - name: gtm
      - name: type
      - name: url_slug
      - name: full_value

  - name: feature_flags_source
    description: Source layer for a list of all feature flags
    columns:
      - name: name
      - name: type
      - name: group
      - name: milestone
      - name: default_enabled

  - name: geozones_yaml_source
    description: Source layer for extracting and cleaning data from [geo_zones.yml](https://gitlab.com/gitlab-com/people-group/peopleops-eng/compensation-calculator/-/blob/main/data/geo_zones.yml)
    columns:
      - name: unique_key
      - name: geozone_title
      - name: geozone_factor
      - name: country
      - name: state_or_province
      - name: uploaded_at
  
  - name: location_factors_yaml_source
    description: Emphemeral layer for extracting and cleaning data from [location_factors.yml file](https://gitlab.com/gitlab-com/www-gitlab-com/blob/master/data/location_factors.yml) in the GitLab Handbook.

  - name: **********************_corporate_finance_source
    description: Emphemeral layer for extracting and cleaning data from [corporate_finance.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/corporate_finance.yml) in the GitLab Handbook.

  - name: **********************_cost_source
    description: Emphemeral layer for extracting and cleaning data from [chief_of_staff_team.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/chief_of_staff_team.yml) in the GitLab Handbook.

  - name: **********************_customer_support_source
    description: Emphemeral layer for extracting and cleaning data from [chief_of_staff_team.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/customer_support_department.yml) in the GitLab Handbook.

  - name: **********************_dev_section_source
    description: Emphemeral layer for extracting and cleaning data from [dev_section.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/dev_section.yml) in the GitLab Handbook.

  - name: **********************_development_department_source
    description: Emphemeral layer for extracting and cleaning data from [dev_section.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/development_department.yml) in the GitLab Handbook.

  - name: **********************_enablement_section_source
    description: Emphemeral layer for extracting and cleaning data from [enablement_section.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/enablement_section.yml) in the GitLab Handbook.

  - name: **********************_engineering_source
    description: Emphemeral layer for extracting and cleaning data from [engineering_function.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/engineering_function.yml) in the GitLab Handbook.

  - name: **********************_finance_source
    description: Emphemeral layer for extracting and cleaning data from [finance_team.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/finance_team.yml) in the GitLab Handbook.

  - name: **********************_infrastructure_department_source
    description: Emphemeral layer for extracting and cleaning data from [finance_team.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/infrastructure_department.yml) in the GitLab Handbook.

  - name: **********************_marketing_source
    description: Emphemeral layer for extracting and cleaning data from [marketing.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/marketing.yml) in the GitLab Handbook.

  - name: **********************_ops_section_source
    description: Emphemeral layer for extracting and cleaning data from [ops_section.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/ops_section.yml) in the GitLab Handbook.

  - name: **********************_people_success_source
    description: Emphemeral layer for extracting and cleaning data from [people_success.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/people_success.yml) in the GitLab Handbook.

  - name: **********************_product_source
    description: Emphemeral layer for extracting and cleaning data from [product.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/product.yml) in the GitLab Handbook.

  - name: **********************_quality_department_source
    description: Emphemeral layer for extracting and cleaning data from [quality_department.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/quality_department.yml) in the GitLab Handbook.

  - name: **********************_recruiting_source
    description: Emphemeral layer for extracting and cleaning data from [recruiting.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/recruiting.yml) in the GitLab Handbook.

  - name: **********************_sales_source
    description: Emphemeral layer for extracting and cleaning data from [sales.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/sales.yml) in the GitLab Handbook.

  - name: **********************_security_department_source
    description: Emphemeral layer for extracting and cleaning data from [security_department.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/security_department.yml) in the GitLab Handbook.

  - name: **********************_ux_department_source
    description: Emphemeral layer for extracting and cleaning data from [ux_department.yml](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/**********************/ux_department.yml) in the GitLab Handbook.

  - name: roles_yaml_source
    description: Emphemeral layer for extracting and cleaning data from [roles.yml file](https://gitlab.com/gitlab-com/www-gitlab-com/blob/master/data/roles.yml) in the GitLab Handbook.

  - name: stages_yaml_source
    description: Emphemeral layer for extracting and cleaning data from [stages.yml file](https://gitlab.com/gitlab-com/www-gitlab-com/blob/master/data/stages.yml) in the GitLab Handbook.

  - name: releases_source
    description: Ephemeral layer for extracting and cleaning data from [releases.yml file](https://gitlab.com/gitlab-com/www-gitlab-com/-/blob/master/data/releases.yml)
  
  - name: team_yaml_source
    description: Emphemeral layer for extracting and cleaning data from [team.yml file](https://gitlab.com/gitlab-com/www-gitlab-com/blob/master/data/team.yml) in the GitLab Handbook.
  
  - name: usage_ping_metrics_source
    description: Layer for extracting and cleaning data from [unified usage ping metrics file](https://gitlab.com/api/v4/usage_data/metric_definitions).

  - name: geozones_yaml_flatten_source
    description: Source layer reducing flattening data from [geo_zones.yml](https://gitlab.com/gitlab-com/people-group/peopleops-eng/compensation-calculator/-/blob/main/data/geo_zones.yml)
    columns:
      - name: valid_from
        description: The earliest date the localities and factors were is in a source file.
      - name: valid_to
        description: The latest date the localities and factors were is in a source file.
      - name: is_current
        description: A flag to indicate if the row is from the most recent loaded file.
      - name: geozone_title
        description: The top level grouping from the source file
      - name: geozone_factor
        description: The factor from the top level of the source file
      - name: country
        description: The second level grouping from the source file representing the countries within a geozone
      - name: state_or_province
        description: The third level grouping from the source file representing the states or provinces within a geozone

  - name: location_factors_yaml_flatten_source
    description: Source layer reducing flattening data from [location_factors.yml](https://gitlab.com/gitlab-com/people-group/peopleops-eng/compensation-calculator/-/blob/main/data/location_factors.yml)
    columns:
      - name: valid_from
        description: The earliest date the localities and factors were is in a source file.
      - name: valid_to
        description: The latest date the localities and factors were is in a source file.
      - name: is_current
        description: A flag to indicate if the row is from the most recent loaded file.
      - name: area_level_1
        description: The area from the top level of the grouping within the file. Not used in source file after 2020-12-10.
      - name: country_level_1
        description: The country from the top level of the grouping within the file
      - name: locationfactor_level_1
        description: The location factor from the top level of the grouping within the file. Not used in source file after 2020-12-10.
      - name: factor_level_1
        description: The factor from the top level of the grouping within the file.
      - name: metro_areas_name_level_2
        description: A second level grouping from with the file represents metro areas within a country.
      - name: metro_areas_factor_level_2
        description: The factor from the second level grouping from with in the file for same level metro area.
      - name: metro_areas_sub_location_level_2
        description: An attribute of the metro area, typically representing a state or province.
      - name: states_or_provinces_name_level_2
        description: A second level grouping from within the file representing a state or province within a country.
      - name: states_or_provinces_factor_level_2
        description: The factor from the second level grouping from within the file for the same level state or province.
      - name: states_or_provinces_metro_areas_name_level_2
        description: A third level grouping from within the file representing a metro area within a state or province.
      - name: states_or_provinces_metro_areas_factor_level_2
        description: The factor from the third level grouping from within the file for the same level metro area of a state or province.

  - name: director_location_factor_seed_source
    description: Source layer cleaning flattening data from director_location_factors.csv
    columns:
      - name: country
        description: The parent country of the locality.
      - name: locality
        description: The director locality.
      - name: factor
        description: The factor for the given locality and validity range
      - name: valid_from
        description: The earliest date the localities and factors were is in a source file.
      - name: valid_to
        description: The earliest date the localities and factors were is in a source file.
      - name: is_current
        description: The earliest date the localities and factors were is in a source file.
