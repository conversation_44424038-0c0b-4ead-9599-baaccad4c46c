version: 2

sources:
  - name: gitlab_data_yaml
    database: '{{ env_var("SNOWFLAKE_LOAD_DATABASE") }}'
    schema: gitlab_data_yaml
    loader: Airflow
    loaded_at_field: uploaded_at
    
    quoting:
      database: false
      schema: false
      identifier: false

    freshness:
        warn_after: {count: 8, period: hour}

    tables:
      - name: categories
      - name: chief_of_staff_team_pi
      - name: corporate_finance_pi
      - name: customer_support_pi
      - name: dev_section_pi
      - name: development_department_pi
      - name: feature_flags
      - name: infrastructure_department_pi
      - name: enablement_section_pi
      - name: engineering_function_pi
      - name: finance_team_pi
      - name: geo_zones
      - name: location_factors
      - name: marketing_pi
      - name: ops_section_pi
      - name: people_success_pi
      - name: product_pi 
      - name: quality_department_pi
      - name: recruiting_pi
      - name: releases
      - name: roles
      - name: sales_pi
      - name: security_department_pi
      - name: stages
      - name: team
      - name: usage_ping_metrics
      - name: ux_department_pi
      - name: cloud_connector
      - name: content_keystone
      - name: cloud_connector_unit_primitives
