{{ config(
    tags=["mnpi"]
) }}

WITH source AS (

  SELECT *
  FROM {{ref('sfdc_opportunity_snapshots_source')}}
  WHERE dbt_valid_to IS NULL

),

renamed AS (
  SELECT
    -- keys
    account_id,
    opportunity_id,
    opportunity_name,
    owner_id,

    -- logistical information
    is_closed,
    is_won,
    valid_deal_count,
    close_date,
    created_date,
    deployment_preference,
    generated_source,
    lead_source,
    merged_opportunity_id,
    duplicate_opportunity_id,
    contract_reset_opportunity_id,
    account_owner,
    opportunity_owner,
    opportunity_owner_manager,
    opportunity_owner_department,
    crm_sales_dev_rep_id,
    crm_business_dev_rep_id,
    crm_business_dev_rep_id_lookup,
    opportunity_development_representative,
    sales_accepted_date,
    sales_path,
    sales_qualified_date,
    iqm_submitted_by_role,
    subscription_type,
    stage_name,

    -- opportunity information
    acv,
    amount,
    competitors,
    critical_deal_flag,
    forecast_category_name,
    forecasted_iacv,
    iacv_created_date,
    incremental_acv,
    invoice_number,
    is_refund,
    is_downgrade,
    is_swing_deal,
    is_edu_oss,
    is_ps_opp,
    net_incremental_acv,
    primary_campaign_source_id,
    probability,
    professional_services_value,
    edu_services_value,
    investment_services_value,
    pushed_count,
    reason_for_loss,
    reason_for_loss_details,
    refund_iacv,
    downgrade_iacv,
    renewal_acv,
    renewal_amount,
    number_of_sa_activity_tasks,
    sdr_pipeline_contribution,
    solutions_to_be_replaced,
    technical_evaluation_date,
    total_contract_value,
    recurring_amount,
    true_up_amount,
    proserv_amount,
    other_non_recurring_amount,
    upside_swing_deal_iacv,
    is_web_portal_purchase,
    opportunity_term,
    partner_initiated_opportunity,
    user_segment,
    subscription_start_date,
    subscription_end_date,
    subscription_renewal_date,
    true_up_value,
    order_type_current,
    order_type_stamped,
    net_arr,
    arr_basis,
    arr,
    xdr_net_arr_stage_3,
    xdr_net_arr_stage_1,
    net_arr_stage_1,
    enterprise_agile_planning_net_arr,
    duo_net_arr,
    days_in_sao,
    new_logo_count,
    user_geo_stamped,
    user_region_stamped,
    user_area_stamped,
    crm_opp_owner_user_role_type_stamped,
    user_business_unit_stamped,
    crm_opp_owner_stamped_name,
    crm_account_owner_stamped_name,
    sao_crm_opp_owner_sales_segment_stamped,
    sao_crm_opp_owner_sales_segment_geo_region_area_stamped,
    sao_crm_opp_owner_geo_stamped,
    sao_crm_opp_owner_region_stamped,
    sao_crm_opp_owner_area_stamped,
    opportunity_category,
    opportunity_health,
    risk_type,
    risk_reasons,
    tam_notes,
    primary_solution_architect,
    product_details,
    product_category,
    products_purchased,
    payment_schedule,
    comp_new_logo_override,
    is_pipeline_created_eligible,
    next_steps,
    auto_renewal_status,
    qsr_notes,
    qsr_status,
    manager_confidence,
    renewal_risk_category,
    renewal_swing_arr,
    renewal_manager,
    renewal_forecast_health,
    startup_type,

    -- dates in stage fields
    days_in_0_pending_acceptance,
    days_in_1_discovery,
    days_in_2_scoping,
    days_in_3_technical_evaluation,
    days_in_4_proposal,
    days_in_5_negotiating,
    stage_0_pending_acceptance_date,
    stage_1_discovery_date,
    stage_2_scoping_date,
    stage_3_technical_evaluation_date,
    stage_4_proposal_date,
    stage_5_negotiating_date,
    stage_6_awaiting_signature_date,
    stage_6_closed_won_date,
    stage_6_closed_lost_date,

    -- channel reporting
    -- original issue: https://gitlab.com/gitlab-data/analytics/-/issues/6072
    dr_partner_deal_type,
    dr_partner_engagement,
    dr_deal_id,
    dr_primary_registration,
    partner_account,
    dr_status,
    distributor,
    influence_partner,
    is_focus_partner,
    fulfillment_partner,
    platform_partner,
    partner_track,
    resale_partner_track,
    is_public_sector_opp,
    is_registration_from_portal,
    calculated_discount,
    partner_discount,
    partner_discount_calc,
    partner_margin_percentage,
    comp_channel_neutral,
    aggregate_partner,

    -- command plan fields
    cp_champion,
    cp_close_plan,
    cp_decision_criteria,
    cp_decision_process,
    cp_economic_buyer,
    cp_help,
    cp_identify_pain,
    cp_metrics,
    cp_partner,
    cp_paper_process,
    cp_review_notes,
    cp_risks,
    cp_use_cases,
    cp_value_driver,
    cp_why_do_anything_at_all,
    cp_why_gitlab,
    cp_why_now,
    cp_score,

    -- original issue: https://gitlab.com/gitlab-data/analytics/-/issues/6577
    sa_tech_evaluation_close_status,
    sa_tech_evaluation_end_date,
    sa_tech_evaluation_start_date,

    -- flag to identify eligible booking deals, excluding jihu - issue: https://gitlab.com/gitlab-com/sales-team/field-operations/systems/-/issues/1805
    fpa_master_bookings_flag,
    downgrade_reason,
    ssp_id,
    ga_client_id,

    -- vsa data - issue: https://gitlab.com/gitlab-com/sales-team/field-operations/customer-success-operations/-/issues/2399
    vsa_readout,
    vsa_start_date_net_arr,
    vsa_start_date,
    vsa_url,
    vsa_status,
    vsa_end_date,

    -- original issue: https://gitlab.com/gitlab-com/sales-team/field-operations/customer-success-operations/-/issues/2464
    downgrade_details,
    won_arr_basis_for_clari,
    arr_basis_for_clari,
    forecasted_churn_for_clari,
    override_arr_basis_clari,

    -- ps fields - issue: https://gitlab.com/gitlab-com/sales-team/field-operations/customer-success-operations/-/issues/2723
    intended_product_tier,
    parent_opportunity_id,

    -- ptc fields - issue: https://gitlab.com/gitlab-data/analytics/-/issues/19440
    ptc_predicted_arr,
    ptc_predicted_renewal_risk_category,

    -- metadata
    is_deleted,
    last_activity_date,
    sales_last_activity_date,
    record_type_id
  FROM source
  
)

SELECT *
FROM renamed
