version: 2

sources:
  - name: salesforce
    tags: ["tdf","sfdc"]
    database: '{{ env_var("SNOWFLAKE_LOAD_DATABASE") }}'
    schema: salesforce_v2_stitch
    loader: Stitch
    loaded_at_field: _sdc_batched_at

    quoting:
      database: false
      schema: false
      identifier: false

    freshness:
        warn_after: {count: 8, period: hour}
        error_after: {count: 24, period: hour}

    tables:
      - name: account
        description: '{{ doc("sfdc_account_source") }}'
      - name: account_history
        identifier: accounthistory
        description: '{{ doc("sfdc_accounthistory_source") }}'
      - name: bizible_attribution_touchpoint
        identifier: bizible2__bizible_attribution_touchpoint__c
        description: '{{ doc("sfdc_bizible_source") }}'
      - name: bizible_person
        identifier: bizible2__bizible_person__c
        description: '{{ doc("sfdc_bizible_source") }}'
      - name: bizible_touchpoint
        identifier: bizible2__bizible_touchpoint__c
        description: '{{ doc("sfdc_bizible_source") }}'
      - name: campaign
        description: '{{ doc("sfdc_campaign_source") }}'
      - name: campaign_member
        identifier: campaignmember
        description: '{{ doc("sfdc_campaignmember_source") }}'
      - name: case
        description: '{{ doc("sfdc_case_source") }}'
      - name: casehistory
        description: '{{ doc("sfdc_case_history_source")}}'
      - name: contact
        description: '{{ doc("sfdc_contact_source") }}'
      - name: contact_history
        identifier: contacthistory
        description: '{{ doc("sfdc_contacthistory_source")}}'
      - name: customer_subscription
        identifier: customer_subscription__c
        description: '{{ doc("sfdc_customer_subscription_source")}}'
      - name: event
        description: '{{ doc("sfdc_event_source") }}'
      - name: executive_business_review
        identifier: executive_business_review__c
        description: '{{ doc("sfdc_execbus_source") }}'
      - name: hg_insights_technographics
        identifier: HG_Insights__HGTechnographic__c
        description: 'Raw HG Insight Data'
      - name: lead
        description: '{{ doc("sfdc_lead_source") }}'
      - name: lead_history
        identifier: leadhistory
        description: '{{ doc("sfdc_leadhistory_source")}}'
      - name: opportunity_field_history
        identifier: opportunityfieldhistory
        description: '{{ doc("sfdc_oppfieldhistory_source") }}'
      - name: opportunity_stage
        identifier: opportunitystage
        description: '{{ doc("sfdc_oppstage_source") }}'
      - name: opportunity_split
        identifier: opportunitysplit
        description: '{{ doc("sfdc_opportunity_split") }}'
      - name: opportunity_split_type
        identifier: opportunitysplittype
        description: '{{ doc("sfdc_opportunity_split_type") }}'
      - name: opportunity_team_member
        identifier: opportunityteammember
        description: '{{ doc("sfdc_opportunity_team_member") }}'
      - name: opportunity
        description: '{{ doc("sfdc_opp_source") }}'
      - name: opportunity_contact_role
        identifier: opportunitycontactrole
        description: '{{ doc("sfdc_contact_role_source")}}'
      - name: opportunity_product
        identifier: opportunitylineitem
        description: '{{ doc("sfdc_opportunity_product_source")}}'
      - name: opportunity_history
        identifier: opportunityhistory
        description: '{{ doc("sfdc_opphistory_source") }}'
      - name: proof_of_concept
        identifier: proof_of_concept__c
        description: '{{ doc("sfdc_pov_source") }}'
      - name: quote
        identifier: zqu__quote__c
        description: '{{ doc("sfdc_quote_source") }}'
      - name: record_type
        identifier: recordtype
        description: '{{ doc("sfdc_recordtype_source") }}'
      - name: statement_of_work
        identifier: statement_of_work__c
        description: '{{ doc("sfdc_professional_services_engagement_source") }}'
      - name: task
        description: '{{ doc("sfdc_task_source") }}'
      - name: traction_history_log
        identifier: tracrtc__history_log__c
        description: SFDC's Traction history log source table.
      - name: user_role
        identifier: userrole
        description: '{{ doc("sfdc_userrole_source") }}'
      - name: user
        description: '{{ doc("sfdc_user_source") }}'
      - name: zoom_webinar
        identifier: zoom_app__zoom_webinar__c
        description: '{{ doc("sfdc_zoom_source") }}'
      - name: zoom_webinar_attendee
        identifier: zoom_app__zoom_webinar_attendee__c
        description: '{{ doc("sfdc_zoom_source") }}'
      - name: zoom_webinar_history
        identifier: zoom_app__zoom_webinar_history__c
        description: '{{ doc("sfdc_zoom_source") }}'
      - name: zoom_webinar_registrant
        identifier: zoom_app__zoom_webinar_registrant__c
        description: '{{ doc("sfdc_zoom_source") }}'
      - name: zqu_quote_history
        identifier: zqu__quote__history
      - name: zqu_quote
        identifier: zqu__quote__c
      - name: zqu_quote_amendment
        identifier: zqu__quoteamendment__c
      - name: zqu_quote_rate_plan
        identifier: zqu__quoterateplan__c
        description: '{{ doc("sfdc_zqu_quote_rate_plan_source") }}'
      - name: zqu_quote_rate_plan_charge
        identifier: zqu__quoterateplancharge__c
        description: '{{ doc("sfdc_zqu_quote_rate_plan_charge_source")}}'
      - name: impartner_mdf_funds_claim
        identifier: impartnermdf__fundsclaim__c
        description: '{{ doc("sfdc_impartner_mdf_funds_claim_source") }}'
      - name: impartner_mdf_funds_request
        identifier: impartnermdf__fundsrequest__c
        description: '{{ doc("sfdc_impartner_mdf_funds_request_source") }}'
      - name: vartopia_drs_registration
        identifier: vartopiadrs__registration__c
        description: '{{ doc("sfdc_vartopia_drs_registration_source") }}'
      - name: permission_set_assignment
        identifier: permissionsetassignment
      - name: profile
        identifier: profile
      - name: group_member
        identifier: groupmember
      - name: group
        identifier: '"GROUP"'
      - name: account_share
        identifier: accountshare
      - name: account_team_member
        identifier: accountteammember
      - name: opportunity_share
        identifier: opportunityshare
      - name: user_territory_association
        identifier: userterritory2association
      - name: zqu_product_rate_plan
        identifier: zqu__productrateplan__c
        description: '{{ doc("sfdc_zqu_product_rate_plan_source")}}'
      - name: zqu_zproduct
        identifier: zqu__zproduct__c
        description: '{{ doc("sfdc_zqu_zproduct_source")}}'
    


