version: 2

sources:
  - name: mailgun
    database: '{{ env_var("SNOWFLAKE_LOAD_DATABASE") }}'
    schema: mailgun
    loaded_at_field: uploaded_at
    loader: custom
    description: Mailgun data (loaded by custom connector)

    quoting:
      database: false
      schema: false
      identifier: false


    tables:
      - name: events_rejected
      - name: events_delivered
      - name: events_failed
      - name: events_opened
      - name: events_clicked
      - name: events_unsubscribed
      - name: events_complained