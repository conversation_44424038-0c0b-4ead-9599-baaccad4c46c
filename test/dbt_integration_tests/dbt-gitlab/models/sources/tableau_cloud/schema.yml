version: 2

models:
    - name: tableau_cloud_events_source
      description: "{{ doc('tableau_cloud_events') }}"
      data_tests:
        - dbt_utils.unique_combination_of_columns:
            combination_of_columns:
              - site_luid
              - event_id
      columns:
        - name: site_luid
          data_tests:
              - not_null
        - name: historical_project_name
        - name: site_name
        - name: actor_user_id
        - name: target_user_id
        - name: actor_user_name
        - name: event_name
        - name: event_type
        - name: event_id
          data_tests:
              - not_null
        - name: event_at
        - name: actor_license_role
        - name: actor_site_role
        - name: item_type
        - name: item_id
        - name: item_luid
        - name: item_name
        - name: workbook_name
        - name: historical_item_name
        - name: project_name
        - name: item_owner_id
        - name: item_owner_email
        - name: item_repository_url
        - name: historical_item_repository_url
        - name: admin_insights_published_at
        - name: uploaded_at

    - name: tableau_cloud_groups_source
      description: "{{ doc('tableau_cloud_groups') }}"
      data_tests:
        - dbt_utils.unique_combination_of_columns:
            combination_of_columns:
              - site_luid
              - group_luid
              - user_luid
      columns:
        - name: site_luid
          data_tests:
              - not_null
        - name: group_luid
          data_tests:
              - not_null
        - name: user_luid
        - name: site_name
        - name: group_name
        - name: user_email
        - name: group_minium_site_role
        - name: is_license_on_sign_in
        - name: admin_insights_published_at
        - name: uploaded_at

    - name: tableau_cloud_permissions_source
      description: "{{ doc('tableau_cloud_permissions') }}"
      data_tests:
        - dbt_utils.unique_combination_of_columns:
            combination_of_columns:
              - site_luid
              - grantee_luid
              - item_luid
              - capability_type
              - permission_value
      column:
        - name: grantee_type
        - name: grantee_luid
          data_tests:
              - not_null
        - name: grantee_name
        - name: item_hyperlink
        - name: item_luid
          data_tests:
              - not_null
        - name: item_name
        - name: item_type
        - name: item_parent_project_name
        - name: top_parent_project_name
        - name: controlling_permissions_project_name
        - name: capability_type
          data_tests:
              - not_null
        - name: permission_value
          data_tests:
              - not_null
        - name: permission_description
        - name: site_name
        - name: site_luid
          data_tests:
              - not_null
        - name: user_email
        - name: user_luid
        - name: user_site_role
        - name: admin_insights_published_at
        - name: has_permission
        - name: uploaded_at
    - name: tableau_cloud_site_content_source
      description: "{{ doc('tableau_cloud_site_content') }}"
      data_tests:
        - dbt_utils.unique_combination_of_columns:
            combination_of_columns:
              - site_luid
              - item_luid
      columns:
        - name: created_at
        - name: data_source_content_type
        - name: data_source_database_type
        - name: data_source_is_certified
        - name: item_description
        - name: extracts_incremented_at
        - name: extracts_refreshed_at
        - name: first_published_at
        - name: has_incrementable_extract
        - name: has_refresh_scheduled
        - name: has_refreshable_extract
        - name: is_data_extract
        - name: item_hyperlink
        - name: item_id
        - name: item_luid
          data_tests:
              - not_null
        - name: item_name
        - name: item_parent_project_id
        - name: item_parent_project_level
        - name: item_parent_project_name
        - name: item_parent_project_owner_email
        - name: item_revision
        - name: item_type
        - name: last_accessed_at
        - name: last_published_at
        - name: owner_email
        - name: project_level
        - name: is_controlled_permissions_enabled
        - name: is_nested_projects_permissions_enabled
        - name: controlling_permissions_project_luid
        - name: controlling_permissions_project_name
        - name: site_hyperlink
        - name: site_luid
          data_tests:
              - not_null
        - name: site_name
        - name: size_bytes
        - name: size_mb
        - name: total_size_bytes
        - name: total_size_mb
        - name: storage_quota_bytes
        - name: top_parent_project_name
        - name: updated_at
        - name: view_title
        - name: view_type
        - name: view_workbook_id
        - name: view_workbook_name
        - name: does_workbook_shows_sheets_as_tabs
        - name: admin_insights_published_at
        - name: uploaded_at

    - name: tableau_cloud_users_source
      description: "{{ doc('tableau_cloud_users') }}"
      data_tests:
        - dbt_utils.unique_combination_of_columns:
            combination_of_columns:
              - site_luid
              - user_luid
      columns:
          - name: site_luid
            data_tests:
              - not_null
          - name: site_name
          - name: allowed_creators
          - name: allowed_explorers
          - name: total_allowed_licenses
          - name: allowed_viewers
          - name: user_id
          - name: user_luid
            data_tests:
              - not_null
          - name: user_email
          - name: user_name
          - name: user_friendly_name
          - name: user_creation_at
          - name: user_account_age
          - name: last_login_at
          - name: days_since_last_login
          - name: user_license_type
          - name: user_site_role
          - name: user_projects
          - name: user_data_sources
          - name: certified_data_sources
          - name: size_of_data_sources_mb
          - name: user_workbooks
          - name: size_of_workbooks_mb
          - name: user_views
          - name: access_events_data_sources
          - name: access_events_views
          - name: publish_events_data_sources
          - name: publish_events_workbooks
          - name: data_source_last_access_at
          - name: data_source_last_publish_at
          - name: view_last_access_at
          - name: workbook_last_publish_at
          - name: web_authoring_last_access_at
          - name: total_traffic_data_sources
          - name: unique_visitors_data_sources
          - name: total_traffic_views
          - name: unique_visitors_views
          - name: admin_insights_published_at
          - name: tableau_desktop_last_access_at
          - name: tableau_desktop_last_product_version
          - name: tableau_prep_last_access_at
          - name: tableau_prep_last_product_version
          - name: site_version
          - name: total_occupied_licenses
          - name: total_remaining_licenses
          - name: occupied_explorer_licenses
          - name: occupied_viewer_licenses
          - name: occupied_creator_licenses
          - name: uploaded_at