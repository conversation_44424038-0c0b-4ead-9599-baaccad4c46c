version: 2

sources:
  - name: tableau_cloud
    database: '{{ env_var("SNOWFLAKE_LOAD_DATABASE") }}'
    schema: tableau_cloud
    loader: Tableau
    loaded_at_field: UPLOADED_AT

    quoting:
      database: false
      schema: false
      identifier: false

    tables:
      - name: events
        description: "{{ doc('tableau_cloud_events') }}"

      - name: groups
        description: "{{ doc('tableau_cloud_events') }}"
          
      - name: permissions
        description: "{{ doc('tableau_cloud_permissions') }}" 
          
      - name: site_content
        description: "{{ doc('tableau_cloud_site_content') }}" 
          
      - name: users
        description: "{{ doc('tableau_cloud_users') }}"
          