version: 2

models:
  - name: marketo_lead_source
    description: This table has marketo lead information.
    columns:
      - name: marketo_lead_id
        data_tests:
          - not_null
          - unique
  - name: marketo_activity_add_to_list_source
    columns:
      - name: marketo_activity_add_to_list_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_add_to_opportunity_source
    columns:
      - name: marketo_activity_add_to_opportunity_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_add_to_nurture_source
    columns:
      - name: marketo_activity_add_to_nurture_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_add_to_sfdc_campaign_source
    columns:
      - name: marketo_activity_add_to_sfdc_campaign_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_call_webhook_source
    columns:
      - name: marketo_activity_call_webhook_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_change_data_value_source
    columns:
      - name: marketo_activity_change_data_value_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_change_nurture_cadence_source
    columns:
      - name: marketo_activity_change_nurture_cadence_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_change_nurture_track_source
    columns:
      - name: marketo_activity_change_nurture_track_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_change_owner_source
    columns:
      - name: marketo_activity_change_owner_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_change_score_source
    columns:
      - name: marketo_activity_change_score_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_change_segment_source
    columns:
      - name: marketo_activity_change_segment_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_change_status_in_progression_source
    columns:
      - name: marketo_activity_change_status_in_progression_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_change_status_in_sfdc_campaign_source
    columns:
      - name: marketo_activity_change_status_in_sfdc_campaign_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_click_email_source
    columns:
      - name: marketo_activity_click_email_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_click_link_source
    columns:
      - name: marketo_activity_click_link_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
      - name: client_ip_address
        meta:
          sensitive: true
  - name: marketo_activity_convert_lead_source
    columns:
      - name: marketo_activity_convert_lead_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_delete_lead_source
    columns:
      - name: marketo_activity_delete_lead_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_email_bounced_soft_source
    columns:
      - name: marketo_activity_email_bounced_soft_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
      - name: email
        meta:
          sensitive: true
  - name: marketo_activity_email_bounced_source
    columns:
      - name: marketo_activity_email_bounced_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
      - name: email
        meta:
          sensitive: true
  - name: marketo_activity_email_delivered_source
    columns:
      - name: marketo_activity_email_delivered_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_execute_campaign_source
    columns:
      - name: marketo_activity_execute_campaign_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_fill_out_form_source
    columns:
      - name: marketo_activity_fill_out_form_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
      - name: client_ip_address
        meta:
          sensitive: true
  - name: marketo_activity_fill_out_linkedin_lead_gen_form_source
    columns:
      - name: marketo_activity_fill_out_linkedin_lead_gen_form_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_interesting_moment_source
    columns:
      - name: marketo_activity_interesting_moment_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_merge_leads_source
    columns:
      - name: marketo_activity_merge_leads_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_new_lead_source
    columns:
      - name: marketo_activity_new_lead_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
      - name: modifying_user
        meta:
          sensitive: true
  - name: marketo_activity_open_email_source
    columns:
      - name: marketo_activity_open_email_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_push_lead_to_marketo_source
    columns:
      - name: marketo_activity_push_lead_to_marketo_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
      - name: modifying_user
        meta:
          sensitive: true
  - name: marketo_activity_remove_from_list_source
    columns:
      - name: marketo_activity_remove_from_list_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_remove_from_sfdc_campaign_source
    columns:
      - name: marketo_activity_remove_from_sfdc_campaign_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_request_campaign_source
    columns:
      - name: marketo_activity_request_campaign_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_send_alert_source
    columns:
      - name: marketo_activity_send_alert_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
      - name: send_to_list
        meta:
          sensitive: true
  - name: marketo_activity_send_email_source
    columns:
      - name: marketo_activity_send_email_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_sfdc_activity_source
    columns:
      - name: marketo_activity_sfdc_activity_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
      - name: description
        meta:
          sensitive: true
  - name: marketo_activity_sfdc_activity_updated_source
    columns:
      - name: marketo_activity_sfdc_activity_updated_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
      - name: description
        meta:
          sensitive: true
      - name: primary_attribute_value
        meta:
          sensitive: true
  - name: marketo_activity_sync_lead_to_sfdc_source
    columns:
      - name: marketo_activity_sync_lead_to_sfdc_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
      - name: primary_attribute_value
        meta:
          sensitive: true
  - name: marketo_activity_type_source
    columns:
      - name: marketo_activity_type_id
        data_tests:
          - not_null
          - unique
  - name: marketo_activity_unsubscribe_email_source
    columns:
      - name: marketo_activity_unsubscribe_email_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
      - name: client_ip_address
        meta:
          sensitive: true
  - name: marketo_activity_update_opportunity_source
    columns:
      - name: marketo_activity_update_opportunity_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
  - name: marketo_activity_visit_webpage_source
    columns:
      - name: marketo_activity_visit_webpage_id
        data_tests:
          - not_null
          - unique
      - name: lead_id
        data_tests:
          - not_null
      - name: client_ip_address
        meta:
          sensitive: true