version: 2

sources:
  - name: omamori
    database: RAW
    schema: omamori
    loader: Airflow
    loaded_at_field: uploaded_at

    quoting:
      database: false
      schema: false
      identifier: false

    freshness:
      warn_after: {count: 5, period: hour}
      error_after: {count: 10, period: hour}
    tables:
      - name: entity_data_external
        external:
          location: '@raw.omamori.omamori_load_parquet/entity_data'
          file_format: "( type = parquet )"
          auto_refresh: false
          partitions:
            - name: date_part
              data_type: date
              expression: to_date(split_part(metadata$filename, '/', 2),'YYYYMMDD')
      - name: gitlab_deleted_projects_external
        external:
          location: '@raw.omamori.omamori_load_parquet/gitlab_deleted_projects'
          file_format: "( type = parquet )"
          auto_refresh: false
          partitions:
            - name: date_part
              data_type: date
              expression: to_date(split_part(metadata$filename, '/', 2),'YYYYMMDD')
      - name: mitigation_plan_entity_data_external
        external:
          location: '@raw.omamori.omamori_load_parquet/mitigation_plan_entity_data'
          file_format: "( type = parquet )"
          auto_refresh: false
          partitions:
            - name: date_part
              data_type: date
              expression: to_date(split_part(metadata$filename, '/', 2),'YYYYMMDD')
      - name: mitigation_plan_tags_external
        external:
          location: '@raw.omamori.omamori_load_parquet/mitigation_plan_tags'
          file_format: "( type = parquet )"
          auto_refresh: false
          partitions:
            - name: date_part
              data_type: date
              expression: to_date(split_part(metadata$filename, '/', 2),'YYYYMMDD')
      - name: mitigation_plan_template_tags_external
        external:
          location: '@raw.omamori.omamori_load_parquet/mitigation_plan_template_tags'
          file_format: "( type = parquet )"
          auto_refresh: false
          partitions:
            - name: date_part
              data_type: date
              expression: to_date(split_part(metadata$filename, '/', 2),'YYYYMMDD')
      - name: mitigation_plans_external
        external:
          location: '@raw.omamori.omamori_load_parquet/mitigation_plans'
          file_format: "( type = parquet )"
          auto_refresh: false
          partitions:
            - name: date_part
              data_type: date
              expression: to_date(split_part(metadata$filename, '/', 2),'YYYYMMDD')
      - name: rule_evaluation_matches_external
        external:
          location: '@raw.omamori.omamori_load_parquet/rule_evaluation_matches'
          file_format: "( type = parquet )"
          auto_refresh: false
          partitions:
            - name: date_part
              data_type: date
              expression: to_date(split_part(metadata$filename, '/', 2),'YYYYMMDD')
      - name: rule_evaluations_external
        external:
          location: '@raw.omamori.omamori_load_parquet/rule_evaluations'
          file_format: "( type = parquet )"
          auto_refresh: false
          partitions:
            - name: date_part
              data_type: date
              expression: to_date(split_part(metadata$filename, '/', 2),'YYYYMMDD')
      - name: tags_external
        external:
          location: '@raw.omamori.omamori_load_parquet/tags'
          file_format: "( type = parquet )"
          auto_refresh: false
          partitions:
            - name: date_part
              data_type: date
              expression: to_date(split_part(metadata$filename, '/', 2),'YYYYMMDD')
      - name: users_external
        external:
          location: '@raw.omamori.omamori_load_parquet/users'
          file_format: "( type = parquet )"
          auto_refresh: false
          partitions:
            - name: date_part
              data_type: date
              expression: to_date(split_part(metadata$filename, '/', 2),'YYYYMMDD')
