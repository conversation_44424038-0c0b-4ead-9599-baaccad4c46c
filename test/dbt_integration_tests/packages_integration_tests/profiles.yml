default:
  target: embucket
  outputs:
    snowflake:
      type: snowflake
      threads: 4
      account: "{{ env_var('SNOWFLAKE_ACCOUNT') }}" 
      user: "{{ env_var('SNOWFLAKE_USER') }}" 
      password: "{{ env_var('DBT_ENV_SECRET_SNOWFLAKE_PASS') }}" 
      database: "{{ env_var('SNOWFLAKE_DATABASE') }}" 
      role: "{{ env_var('SNOWFLAKE_ROLE') }}" 
      warehouse: "{{ env_var('SNOWFLAKE_WAREHOUSE') }}" 
      schema: "{{ env_var('SNOWFLAKE_SCHEMA') }}" 
      client_session_keep_alive: True

    embucket:
      type: snowflake
      host: "{{ env_var('EMBUCKET_HOST') }}"
      port: 3000
      protocol: "{{ env_var('EMBUCKET_PROTOCOL') }}"
      account: "test"
      user: "{{ env_var('EMBUCKET_USER') }}"
      password: "{{ env_var('EMBUCKET_PASSWORD') }}"
      role: "{{ env_var('EMBUCKET_ROLE') }}"
      database: "embucket"
      warehouse: "{{ env_var('EMBUCKET_WAREHOUSE') }}"
      schema: "{{ env_var('EMBUCKET_SCHEMA') }}"
      threads: 32
