---
source: crates/df-catalog/src/tests/information_schema.rs
description: SELECT name FROM embucket.information_schema.df_settings
snapshot_kind: text
---
+-------------------------------------------------------------------------+
| name                                                                    |
+-------------------------------------------------------------------------+
| datafusion.catalog.create_default_catalog_and_schema                    |
| datafusion.catalog.default_catalog                                      |
| datafusion.catalog.default_schema                                       |
| datafusion.catalog.format                                               |
| datafusion.catalog.has_header                                           |
| datafusion.catalog.information_schema                                   |
| datafusion.catalog.location                                             |
| datafusion.catalog.newlines_in_values                                   |
| datafusion.execution.batch_size                                         |
| datafusion.execution.coalesce_batches                                   |
| datafusion.execution.collect_statistics                                 |
| datafusion.execution.enable_recursive_ctes                              |
| datafusion.execution.enforce_batch_size_in_joins                        |
| datafusion.execution.keep_partition_by_columns                          |
| datafusion.execution.listing_table_ignore_subdirectory                  |
| datafusion.execution.max_buffered_batches_per_output_file               |
| datafusion.execution.meta_fetch_concurrency                             |
| datafusion.execution.minimum_parallel_output_files                      |
| datafusion.execution.parquet.allow_single_file_parallelism              |
| datafusion.execution.parquet.binary_as_string                           |
| datafusion.execution.parquet.bloom_filter_fpp                           |
| datafusion.execution.parquet.bloom_filter_ndv                           |
| datafusion.execution.parquet.bloom_filter_on_read                       |
| datafusion.execution.parquet.bloom_filter_on_write                      |
| datafusion.execution.parquet.coerce_int96                               |
| datafusion.execution.parquet.column_index_truncate_length               |
| datafusion.execution.parquet.compression                                |
| datafusion.execution.parquet.created_by                                 |
| datafusion.execution.parquet.data_page_row_count_limit                  |
| datafusion.execution.parquet.data_pagesize_limit                        |
| datafusion.execution.parquet.dictionary_enabled                         |
| datafusion.execution.parquet.dictionary_page_size_limit                 |
| datafusion.execution.parquet.enable_page_index                          |
| datafusion.execution.parquet.encoding                                   |
| datafusion.execution.parquet.max_row_group_size                         |
| datafusion.execution.parquet.max_statistics_size                        |
| datafusion.execution.parquet.maximum_buffered_record_batches_per_stream |
| datafusion.execution.parquet.maximum_parallel_row_group_writers         |
| datafusion.execution.parquet.metadata_size_hint                         |
| datafusion.execution.parquet.pruning                                    |
| datafusion.execution.parquet.pushdown_filters                           |
| datafusion.execution.parquet.reorder_filters                            |
| datafusion.execution.parquet.schema_force_view_types                    |
| datafusion.execution.parquet.skip_arrow_metadata                        |
| datafusion.execution.parquet.skip_metadata                              |
| datafusion.execution.parquet.statistics_enabled                         |
| datafusion.execution.parquet.statistics_truncate_length                 |
| datafusion.execution.parquet.write_batch_size                           |
| datafusion.execution.parquet.writer_version                             |
| datafusion.execution.planning_concurrency                               |
| datafusion.execution.skip_partial_aggregation_probe_ratio_threshold     |
| datafusion.execution.skip_partial_aggregation_probe_rows_threshold      |
| datafusion.execution.skip_physical_aggregate_schema_check               |
| datafusion.execution.soft_max_rows_per_output_file                      |
| datafusion.execution.sort_in_place_threshold_bytes                      |
| datafusion.execution.sort_spill_reservation_bytes                       |
| datafusion.execution.split_file_groups_by_statistics                    |
| datafusion.execution.target_partitions                                  |
| datafusion.execution.time_zone                                          |
| datafusion.execution.use_row_number_estimates_to_optimize_partitioning  |
| datafusion.explain.format                                               |
| datafusion.explain.logical_plan_only                                    |
| datafusion.explain.physical_plan_only                                   |
| datafusion.explain.show_schema                                          |
| datafusion.explain.show_sizes                                           |
| datafusion.explain.show_statistics                                      |
| datafusion.optimizer.allow_symmetric_joins_without_pruning              |
| datafusion.optimizer.default_filter_selectivity                         |
| datafusion.optimizer.enable_distinct_aggregation_soft_limit             |
| datafusion.optimizer.enable_round_robin_repartition                     |
| datafusion.optimizer.enable_topk_aggregation                            |
| datafusion.optimizer.expand_views_at_output                             |
| datafusion.optimizer.filter_null_join_keys                              |
| datafusion.optimizer.hash_join_single_partition_threshold               |
| datafusion.optimizer.hash_join_single_partition_threshold_rows          |
| datafusion.optimizer.max_passes                                         |
| datafusion.optimizer.prefer_existing_sort                               |
| datafusion.optimizer.prefer_existing_union                              |
| datafusion.optimizer.prefer_hash_join                                   |
| datafusion.optimizer.repartition_aggregations                           |
| datafusion.optimizer.repartition_file_min_size                          |
| datafusion.optimizer.repartition_file_scans                             |
| datafusion.optimizer.repartition_joins                                  |
| datafusion.optimizer.repartition_sorts                                  |
| datafusion.optimizer.repartition_windows                                |
| datafusion.optimizer.skip_failed_rules                                  |
| datafusion.optimizer.top_down_join_key_reordering                       |
| datafusion.sql_parser.collect_spans                                     |
| datafusion.sql_parser.dialect                                           |
| datafusion.sql_parser.enable_ident_normalization                        |
| datafusion.sql_parser.enable_options_value_normalization                |
| datafusion.sql_parser.map_varchar_to_utf8view                           |
| datafusion.sql_parser.parse_float_as_decimal                            |
| datafusion.sql_parser.recursion_limit                                   |
| datafusion.sql_parser.support_varchar_with_length                       |
+-------------------------------------------------------------------------+
