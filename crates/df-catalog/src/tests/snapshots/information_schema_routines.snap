---
source: crates/df-catalog/src/tests/information_schema.rs
description: "SELECT routine_name FROM embucket.information_schema.routines\n    GROUP BY routine_name ORDER BY routine_name"
snapshot_kind: text
---
+------------------------------------+
| routine_name                       |
+------------------------------------+
| abs                                |
| acos                               |
| acosh                              |
| approx_distinct                    |
| approx_median                      |
| approx_percentile_cont             |
| approx_percentile_cont_with_weight |
| array_agg                          |
| array_any_value                    |
| array_append                       |
| array_cat                          |
| array_concat                       |
| array_contains                     |
| array_dims                         |
| array_distance                     |
| array_distinct                     |
| array_element                      |
| array_empty                        |
| array_except                       |
| array_extract                      |
| array_has                          |
| array_has_all                      |
| array_has_any                      |
| array_indexof                      |
| array_intersect                    |
| array_join                         |
| array_length                       |
| array_max                          |
| array_ndims                        |
| array_pop_back                     |
| array_pop_front                    |
| array_position                     |
| array_positions                    |
| array_prepend                      |
| array_push_back                    |
| array_push_front                   |
| array_remove                       |
| array_remove_all                   |
| array_remove_n                     |
| array_repeat                       |
| array_replace                      |
| array_replace_all                  |
| array_replace_n                    |
| array_resize                       |
| array_reverse                      |
| array_slice                        |
| array_sort                         |
| array_to_string                    |
| array_union                        |
| arrays_overlap                     |
| arrow_cast                         |
| arrow_typeof                       |
| ascii                              |
| asin                               |
| asinh                              |
| atan                               |
| atan2                              |
| atanh                              |
| avg                                |
| bit_and                            |
| bit_length                         |
| bit_or                             |
| bit_xor                            |
| bool_and                           |
| bool_or                            |
| btrim                              |
| cardinality                        |
| cbrt                               |
| ceil                               |
| char_length                        |
| character_length                   |
| chr                                |
| coalesce                           |
| concat                             |
| concat_ws                          |
| contains                           |
| corr                               |
| cos                                |
| cosh                               |
| cot                                |
| count                              |
| covar                              |
| covar_pop                          |
| covar_samp                         |
| cume_dist                          |
| current_date                       |
| current_time                       |
| current_timestamp                  |
| date_bin                           |
| date_format                        |
| date_part                          |
| date_trunc                         |
| datepart                           |
| datetrunc                          |
| decode                             |
| degrees                            |
| dense_rank                         |
| digest                             |
| element_at                         |
| empty                              |
| encode                             |
| ends_with                          |
| exp                                |
| factorial                          |
| find_in_set                        |
| first_value                        |
| flatten                            |
| floor                              |
| from_unixtime                      |
| gcd                                |
| generate_series                    |
| get_field                          |
| greatest                           |
| grouping                           |
| ifnull                             |
| initcap                            |
| instr                              |
| isnan                              |
| iszero                             |
| lag                                |
| last_value                         |
| lcm                                |
| lead                               |
| least                              |
| left                               |
| length                             |
| levenshtein                        |
| list_any_value                     |
| list_append                        |
| list_cat                           |
| list_concat                        |
| list_contains                      |
| list_dims                          |
| list_distance                      |
| list_distinct                      |
| list_element                       |
| list_empty                         |
| list_except                        |
| list_extract                       |
| list_has                           |
| list_has_all                       |
| list_has_any                       |
| list_indexof                       |
| list_intersect                     |
| list_join                          |
| list_length                        |
| list_max                           |
| list_ndims                         |
| list_pop_back                      |
| list_pop_front                     |
| list_position                      |
| list_positions                     |
| list_prepend                       |
| list_push_back                     |
| list_push_front                    |
| list_remove                        |
| list_remove_all                    |
| list_remove_n                      |
| list_repeat                        |
| list_replace                       |
| list_replace_all                   |
| list_replace_n                     |
| list_resize                        |
| list_reverse                       |
| list_slice                         |
| list_sort                          |
| list_to_string                     |
| list_union                         |
| ln                                 |
| log                                |
| log10                              |
| log2                               |
| lower                              |
| lpad                               |
| ltrim                              |
| make_array                         |
| make_date                          |
| make_list                          |
| map                                |
| map_extract                        |
| map_keys                           |
| map_values                         |
| max                                |
| md5                                |
| mean                               |
| median                             |
| min                                |
| named_struct                       |
| nanvl                              |
| now                                |
| nth_value                          |
| ntile                              |
| nullif                             |
| nvl                                |
| nvl2                               |
| octet_length                       |
| overlay                            |
| percent_rank                       |
| pi                                 |
| position                           |
| pow                                |
| power                              |
| radians                            |
| random                             |
| range                              |
| rank                               |
| regexp_count                       |
| regexp_like                        |
| regexp_match                       |
| regexp_replace                     |
| regexp_substr                      |
| regr_avgx                          |
| regr_avgy                          |
| regr_count                         |
| regr_intercept                     |
| regr_r2                            |
| regr_slope                         |
| regr_sxx                           |
| regr_sxy                           |
| regr_syy                           |
| repeat                             |
| replace                            |
| reverse                            |
| right                              |
| rlike                              |
| round                              |
| row                                |
| row_number                         |
| rpad                               |
| rtrim                              |
| sha224                             |
| sha256                             |
| sha384                             |
| sha512                             |
| signum                             |
| sin                                |
| sinh                               |
| split_part                         |
| sqrt                               |
| starts_with                        |
| stddev                             |
| stddev_pop                         |
| stddev_samp                        |
| string_agg                         |
| string_to_array                    |
| string_to_list                     |
| strpos                             |
| struct                             |
| substr                             |
| substr_index                       |
| substring                          |
| substring_index                    |
| sum                                |
| tan                                |
| tanh                               |
| to_char                            |
| to_date                            |
| to_hex                             |
| to_local_time                      |
| to_timestamp                       |
| to_timestamp_micros                |
| to_timestamp_millis                |
| to_timestamp_nanos                 |
| to_timestamp_seconds               |
| to_unixtime                        |
| today                              |
| translate                          |
| trim                               |
| trunc                              |
| union_extract                      |
| upper                              |
| uuid                               |
| var                                |
| var_pop                            |
| var_population                     |
| var_samp                           |
| var_sample                         |
| version                            |
+------------------------------------+
