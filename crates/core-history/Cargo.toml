[package]
name = "core-history"
version = "0.1.0"
edition = "2024"
license-file.workspace = true

[dependencies]
core-utils = { path = "../core-utils" }

bytes = { workspace = true }
chrono = { workspace = true, features = ["serde"] }
serde = { workspace = true }
serde_json = { workspace = true }
snafu = { workspace = true }
slatedb = { workspace = true }
async-trait = { workspace = true }
futures = { workspace = true }
tracing = { workspace = true }
mockall = "0.13.1"

[dev-dependencies]
tokio = { workspace = true }

[lints]
workspace = true
