[package]
name = "api-iceberg-rest"
version = "0.1.0"
edition = "2024"
license-file.workspace = true

[dependencies]
core-metastore = { path = "../core-metastore" }
core-utils = { path = "../core-utils" }

axum = { workspace = true }
http = { workspace = true }
iceberg-rest-catalog = { workspace = true }
iceberg-rust = { workspace = true }
iceberg-rust-spec = { workspace = true }
object_store = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
snafu = { workspace = true }
tracing = { workspace = true }
validator = { workspace = true }


[lints]
workspace = true
