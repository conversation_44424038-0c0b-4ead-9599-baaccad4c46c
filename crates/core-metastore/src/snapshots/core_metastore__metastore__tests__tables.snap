---
source: crates/metastore/src/metastore.rs
expression: "(no_schema_result, table_create, paths, table_list, table_get,\ntable_list_after)"
---
(
    Err(
        SchemaNotFound {
            schema: "testschema",
            db: "testdb",
        },
    ),
    RwObject {
        data: Table {
            ident: TableIdent {
                table: "testtable",
                schema: "testschema",
                database: "testdb",
            },
            metadata: TableMetadata {
                format_version: V2,
                table_uuid: UUID,
                location: "memory:///testdb/testschema/testtable",
                last_sequence_number: 0,
                last_update_ms: "INTEGER",
                last_column_id: 0,
                schemas: {
                    0: Schema {
                        schema_id: 0,
                        identifier_field_ids: None,
                        fields: StructType {
                            fields: [
                                StructField {
                                    id: 0,
                                    name: "id",
                                    required: true,
                                    field_type: Primitive(
                                        Int,
                                    ),
                                    doc: None,
                                },
                                StructField {
                                    id: 1,
                                    name: "name",
                                    required: true,
                                    field_type: Primitive(
                                        String,
                                    ),
                                    doc: None,
                                },
                            ],
                            lookup: {LOOKUPS},
                        },
                    },
                },
                current_schema_id: 0,
                partition_specs: {
                    0: PartitionSpec {
                        spec_id: 0,
                        fields: [],
                    },
                },
                default_spec_id: 0,
                last_partition_id: 0,
                properties: {PROPERTIES},
                current_snapshot_id: None,
                snapshots: {},
                snapshot_log: [],
                metadata_log: [],
                sort_orders: {
                    0: SortOrder {
                        order_id: 0,
                        fields: [],
                    },
                },
                default_sort_order_id: 0,
                refs: {},
            },
            metadata_location: "memory:///testdb/testschema/testtable/metadata/UUID.metadata.json",
            properties: {PROPERTIES},
            volume_ident: None,
            volume_location: None,
            is_temporary: false,
            format: Iceberg,
        },
        created_at: "TIMESTAMP",
        updated_at: "TIMESTAMP",
    },
    Ok(
        [
            Ok(
                ObjectMeta {
                    location: Path {
                        raw: "testdb/testschema/testtable/metadata/UUID.metadata.json",
                    },
                    last_modified: "TIMESTAMP",
                    size: "INTEGER",
                    e_tag: Some(
                        "0",
                    ),
                    version: None,
                },
            ),
        ],
    ),
    [
        RwObject {
            data: Table {
                ident: TableIdent {
                    table: "testtable",
                    schema: "testschema",
                    database: "testdb",
                },
                metadata: TableMetadata {
                    format_version: V2,
                    table_uuid: UUID,
                    location: "memory:///testdb/testschema/testtable",
                    last_sequence_number: 0,
                    last_update_ms: "INTEGER",
                    last_column_id: 0,
                    schemas: {
                        0: Schema {
                            schema_id: 0,
                            identifier_field_ids: None,
                            fields: StructType {
                                fields: [
                                    StructField {
                                        id: 0,
                                        name: "id",
                                        required: true,
                                        field_type: Primitive(
                                            Int,
                                        ),
                                        doc: None,
                                    },
                                    StructField {
                                        id: 1,
                                        name: "name",
                                        required: true,
                                        field_type: Primitive(
                                            String,
                                        ),
                                        doc: None,
                                    },
                                ],
                                lookup: {LOOKUPS},
                            },
                        },
                    },
                    current_schema_id: 0,
                    partition_specs: {
                        0: PartitionSpec {
                            spec_id: 0,
                            fields: [],
                        },
                    },
                    default_spec_id: 0,
                    last_partition_id: 0,
                    properties: {PROPERTIES},
                    current_snapshot_id: None,
                    snapshots: {},
                    snapshot_log: [],
                    metadata_log: [],
                    sort_orders: {
                        0: SortOrder {
                            order_id: 0,
                            fields: [],
                        },
                    },
                    default_sort_order_id: 0,
                    refs: {},
                },
                metadata_location: "memory:///testdb/testschema/testtable/metadata/UUID.metadata.json",
                properties: {PROPERTIES},
                volume_ident: None,
                volume_location: None,
                is_temporary: false,
                format: Iceberg,
            },
            created_at: "TIMESTAMP",
            updated_at: "TIMESTAMP",
        },
    ],
    Some(
        RwObject {
            data: Table {
                ident: TableIdent {
                    table: "testtable",
                    schema: "testschema",
                    database: "testdb",
                },
                metadata: TableMetadata {
                    format_version: V2,
                    table_uuid: UUID,
                    location: "memory:///testdb/testschema/testtable",
                    last_sequence_number: 0,
                    last_update_ms: "INTEGER",
                    last_column_id: 0,
                    schemas: {
                        0: Schema {
                            schema_id: 0,
                            identifier_field_ids: None,
                            fields: StructType {
                                fields: [
                                    StructField {
                                        id: 0,
                                        name: "id",
                                        required: true,
                                        field_type: Primitive(
                                            Int,
                                        ),
                                        doc: None,
                                    },
                                    StructField {
                                        id: 1,
                                        name: "name",
                                        required: true,
                                        field_type: Primitive(
                                            String,
                                        ),
                                        doc: None,
                                    },
                                ],
                                lookup: {LOOKUPS},
                            },
                        },
                    },
                    current_schema_id: 0,
                    partition_specs: {
                        0: PartitionSpec {
                            spec_id: 0,
                            fields: [],
                        },
                    },
                    default_spec_id: 0,
                    last_partition_id: 0,
                    properties: {PROPERTIES},
                    current_snapshot_id: None,
                    snapshots: {},
                    snapshot_log: [],
                    metadata_log: [],
                    sort_orders: {
                        0: SortOrder {
                            order_id: 0,
                            fields: [],
                        },
                    },
                    default_sort_order_id: 0,
                    refs: {},
                },
                metadata_location: "memory:///testdb/testschema/testtable/metadata/UUID.metadata.json",
                properties: {PROPERTIES},
                volume_ident: None,
                volume_location: None,
                is_temporary: false,
                format: Iceberg,
            },
            created_at: "TIMESTAMP",
            updated_at: "TIMESTAMP",
        },
    ),
    [],
)
