---
source: crates/metastore/src/metastore.rs
expression: "(all_volumes, get_volume, all_volumes_after)"
---
(
    [
        RwObject {
            data: Volume {
                ident: "test",
                volume: Memory,
            },
            created_at: "TIMESTAMP",
            updated_at: "TIMESTAMP",
        },
    ],
    Some(
        RwObject {
            data: Volume {
                ident: "test",
                volume: Memory,
            },
            created_at: "TIMESTAMP",
            updated_at: "TIMESTAMP",
        },
    ),
    [],
)
