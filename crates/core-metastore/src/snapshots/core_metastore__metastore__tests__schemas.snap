---
source: crates/metastore/src/metastore.rs
expression: "(no_db_result, schema_create, schema_list, schema_get, schema_list_after)"
---
(
    Err(
        DatabaseNotFound {
            db: "testdb",
        },
    ),
    RwObject {
        data: Schema {
            ident: SchemaIdent {
                schema: "testschema",
                database: "testdb",
            },
            properties: None,
        },
        created_at: "TIMESTAMP",
        updated_at: "TIMESTAMP",
    },
    [
        RwObject {
            data: Schema {
                ident: SchemaIdent {
                    schema: "testschema",
                    database: "testdb",
                },
                properties: None,
            },
            created_at: "TIMESTAMP",
            updated_at: "TIMESTAMP",
        },
    ],
    Some(
        RwObject {
            data: Schema {
                ident: SchemaIdent {
                    schema: "testschema",
                    database: "testdb",
                },
                properties: None,
            },
            created_at: "TIMESTAMP",
            updated_at: "TIMESTAMP",
        },
    ),
    [],
)
