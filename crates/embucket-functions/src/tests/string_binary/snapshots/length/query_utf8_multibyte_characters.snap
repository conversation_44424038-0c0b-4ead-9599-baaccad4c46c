---
source: crates/embucket-functions/src/tests/string_binary/length.rs
description: "\"SELECT \n        LENGTH('Joyeux Noël') AS french,\n        LENGTH('圣诞节快乐') AS chinese,\n        LENGTH('こんにちは') AS japanese,\n        LENGTH('안녕하세요') AS korean\""
---
Ok(
    [
        "+--------+---------+----------+--------+",
        "| french | chinese | japanese | korean |",
        "+--------+---------+----------+--------+",
        "| 11     | 5       | 5        | 5      |",
        "+--------+---------+----------+--------+",
    ],
)
