---
source: crates/embucket-functions/src/tests/aggregate/listagg.rs
description: "\"SELECT LISTAGG(val, ', ') FROM single_item\""
info: "Setup queries: CREATE TABLE single_item AS SELECT * FROM (VALUES ('single')) AS t(val)"
---
Ok(
    [
        "+-------------------------------------+",
        "| listagg(single_item.val,Utf8(\", \")) |",
        "+-------------------------------------+",
        "| single                              |",
        "+-------------------------------------+",
    ],
)
