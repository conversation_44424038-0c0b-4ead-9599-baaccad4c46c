---
source: crates/embucket-functions/src/tests/aggregate/listagg.rs
description: "\"SELECT id, name, LISTAGG(name, ', ') OVER (PARTITION BY id ORDER BY name ROWS UNBOUNDED PRECEDING) AS running_list FROM test_sequences ORDER BY id, name\""
info: "Setup queries: CREATE TABLE test_sequences AS SELECT * FROM (VALUES (1, 'c'), (1, 'a'), (1, 'b'), (2, 'z'), (2, 'y')) AS t(id, name)"
---
Ok(
    [
        "+----+------+--------------+",
        "| id | name | running_list |",
        "+----+------+--------------+",
        "| 1  | a    | a            |",
        "| 1  | b    | a, b         |",
        "| 1  | c    | a, b, c      |",
        "| 2  | y    | y            |",
        "| 2  | z    | y, z         |",
        "+----+------+--------------+",
    ],
)
