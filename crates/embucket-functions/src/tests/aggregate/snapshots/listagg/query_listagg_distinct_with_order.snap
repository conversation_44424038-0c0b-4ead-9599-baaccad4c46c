---
source: crates/embucket-functions/src/tests/aggregate/listagg.rs
description: "\"SELECT LISTAGG(DISTINCT name, ', ') WITHIN GROUP (ORDER BY name) FROM (VALUES ('cherry'), ('apple'), ('banana'), ('apple')) AS t(name)\""
---
Ok(
    [
        "+-------------------------------------+",
        "| listagg(DISTINCT t.name,Utf8(\", \")) |",
        "+-------------------------------------+",
        "| cherry, apple, banana               |",
        "+-------------------------------------+",
    ],
)
