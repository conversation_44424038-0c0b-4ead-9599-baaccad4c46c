---
source: crates/embucket-functions/src/tests/sql/commands/pivot.rs
description: "\"SELECT *\nFROM (\n  SELECT \n    empid,\n    amount,\n    CASE\n      WHEN quarter IN ('2023_Q1', '2023_Q2') THEN 'H1'\n      WHEN quarter IN ('2023_Q3', '2023_Q4') THEN 'H2'\n    END AS half_year\n  FROM quarterly_sales\n)\nPIVOT(SUM(amount) FOR half_year IN ('H1', 'H2'))\nORDER BY empid;\""
info: "Setup queries: CREATE OR REPLACE TABLE quarterly_sales(\n  empid INT,\n  amount INT,\n  quarter TEXT)\n  AS SELECT * FROM VALUES\n    (1, 10000, '2023_Q1'),\n    (1, 400, '2023_Q1'),\n    (2, 4500, '2023_Q1'),\n    (2, 35000, '2023_Q1'),\n    (1, 5000, '2023_Q2'),\n    (1, 3000, '2023_Q2'),\n    (2, 200, '2023_Q2'),\n    (2, 90500, '2023_Q2'),\n    (1, 6000, '2023_Q3'),\n    (1, 5000, '2023_Q3'),\n    (2, 2500, '2023_Q3'),\n    (2, 9500, '2023_Q3'),\n    (3, 2700, '2023_Q3'),\n    (1, 8000, '2023_Q4'),\n    (1, 10000, '2023_Q4'),\n    (2, 800, '2023_Q4'),\n    (2, 4500, '2023_Q4'),\n    (3, 2700, '2023_Q4'),\n    (3, 16000, '2023_Q4'),\n    (3, 10200, '2023_Q4')"
---
Ok(
    [
        "+-------+--------+-------+",
        "| empid | H1     | H2    |",
        "+-------+--------+-------+",
        "| 1     | 18400  | 29000 |",
        "| 2     | 130200 | 17300 |",
        "| 3     |        | 31600 |",
        "+-------+--------+-------+",
    ],
)
