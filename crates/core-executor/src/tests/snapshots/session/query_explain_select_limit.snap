---
source: crates/core-executor/src/tests/query.rs
description: "\"EXPLAIN SELECT * FROM embucket.public.employee_table limit 1\""
info: "Setup queries: SET datafusion.explain.logical_plan_only = true"
snapshot_kind: text
---
Ok(
    [
        "+--------------+---------------------------------------------------------------------------------------------------------------------+",
        "| plan_type    | plan                                                                                                                |",
        "+--------------+---------------------------------------------------------------------------------------------------------------------+",
        "| logical_plan | Limit: skip=0, fetch=1                                                                                              |",
        "|              |   TableScan: embucket.public.employee_table projection=[employee_id, last_name, first_name, department_id], fetch=1 |",
        "+--------------+---------------------------------------------------------------------------------------------------------------------+",
    ],
)
