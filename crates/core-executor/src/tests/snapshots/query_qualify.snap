---
source: crates/runtime/src/tests/queries.rs
description: "\"SELECT product_id, retail_price, quantity, city\n    FROM sales\n    QUALIFY ROW_NUMBER() OVER (PARTITION BY city ORDER BY retail_price) = 1\n    ;\""
---
Ok(
    [
        "+------------+--------------+----------+---------+---------------+",
        "| product_id | retail_price | quantity | city    | qualify_alias |",
        "+------------+--------------+----------+---------+---------------+",
        "| 2          | 5.0          | 16       | Miami   | 1             |",
        "| 2          | 5.0          | 32       | Orlando | 1             |",
        "| 1          | 2.0          | 1        | SF      | 1             |",
        "| 1          | 2.0          | 2        | SJ      | 1             |",
        "+------------+--------------+----------+---------+---------------+",
    ],
)
