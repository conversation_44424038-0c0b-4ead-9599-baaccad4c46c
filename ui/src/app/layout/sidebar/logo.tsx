export const EmbucketLogo = () => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M26.5601 11.3621C26.5888 11.2956 26.6108 11.2269 26.6259 11.1562C26.9703 9.53555 23.5272 7.43053 18.9353 6.45451C14.3435 5.47848 10.3418 6.00105 9.99736 7.6217C9.98232 7.69244 9.9745 7.7641 9.97368 7.83654L8.42412 21.8962C8.37354 22.3551 8.47766 22.8247 8.80354 23.1518C9.69099 24.0424 11.6086 25.344 14.7771 26.0175C17.9457 26.691 20.2269 26.2819 21.3998 25.8292C21.8306 25.663 22.1167 25.2763 22.2572 24.8365L26.5601 11.3621ZM17.6879 12.3234C24.0015 13.6654 26.0944 12.3718 26.5452 11.3952C25.8723 12.8262 22.0459 13.2497 17.6879 12.3234ZM17.6879 12.3234C13.3299 11.3971 10.0066 9.45386 9.97385 7.8729C9.98856 8.94845 11.3745 10.9814 17.6879 12.3234Z"
        fill="#0064D2"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M26.1367 11.0522C26.116 11.1496 26.0746 11.2411 26.0141 11.3269C20.758 8.68747 15.4132 8.21203 12.4356 9.8861C11.1493 9.1558 10.349 8.37165 10.4863 7.72564C10.7734 6.3751 15.0498 6.13978 18.8313 6.94357C22.6128 7.74735 26.4238 9.70169 26.1367 11.0522ZM18.953 24.063L22.6281 13.4222C24.7657 13.3873 25.6205 12.7843 25.8669 12.221L21.9348 24.3815C21.7935 24.8184 21.5087 25.2022 21.0791 25.3644C19.9651 25.7851 17.8319 26.1556 14.8808 25.5283C12.2498 24.9691 10.5353 23.9498 9.59438 23.1419C10.5732 23.4943 11.6443 23.8181 12.7481 24.0527C16.0738 24.7596 18.2704 24.3541 18.953 24.063ZM25.9286 12.0303L25.8669 12.221C25.8951 12.1566 25.9153 12.0928 25.9286 12.0303Z"
        fill="#0054C9"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.8887 7.77571C10.9169 7.7097 11.0094 7.57511 11.314 7.41422C11.6797 7.22102 12.24 7.05481 12.9902 6.95683C14.4822 6.76199 16.525 6.86233 18.7483 7.3349C20.9716 7.80747 22.8786 8.5467 24.1624 9.33154C24.8079 9.72621 25.2521 10.1059 25.5077 10.4312C25.719 10.7002 25.7498 10.8603 25.7492 10.9329C25.7176 11.0035 25.6524 11.1124 25.5206 11.2344C25.465 11.2748 25.3957 11.3182 25.3094 11.3638C24.9437 11.557 24.3835 11.7232 23.6332 11.8212C22.1412 12.016 20.0984 11.9157 17.8751 11.4431C15.6518 10.9705 13.7448 10.2313 12.461 9.44647C11.8155 9.0518 11.3713 8.6721 11.1157 8.34682C11.0353 8.24451 10.9811 8.15794 10.9447 8.08594C10.8979 7.9523 10.8877 7.84704 10.8887 7.77571ZM9.07437 7.78167C9.07836 7.6677 9.09219 7.55192 9.11712 7.43464C9.2806 6.66552 9.85418 6.14961 10.4733 5.82261C11.0982 5.49253 11.89 5.28523 12.7571 5.17199C14.5001 4.94438 16.754 5.07078 19.1226 5.57423C21.4911 6.07769 23.6016 6.87898 25.1013 7.79581C25.8474 8.25198 26.4864 8.76341 26.9231 9.31912C27.3556 9.86966 27.6698 10.5743 27.5063 11.3434C27.4814 11.4607 27.4469 11.5721 27.4042 11.6778L23.1146 25.1103C22.9187 25.7238 22.4827 26.3761 21.724 26.6689C20.3739 27.1899 17.9138 27.6043 14.5901 26.8978C11.2664 26.1914 9.18751 24.8121 8.1661 23.7871C7.59206 23.211 7.45908 22.4377 7.52962 21.7976L7.70728 20.1857C6.86211 20.0819 6.024 19.7889 5.46416 19.1538C4.40603 17.9533 4.76175 16.2344 5.45283 14.7794C6.1418 13.3288 7.33481 11.7463 8.80101 10.262L9.07437 7.78167ZM10.7175 19.9698C10.3124 20.0608 9.91092 20.1297 9.51945 20.1742L9.31879 21.9948C9.28818 22.2725 9.36344 22.4386 9.44116 22.5166C10.1946 23.2728 11.9509 24.4967 14.9643 25.1372C17.9777 25.7777 20.08 25.3739 21.0759 24.9896C21.1786 24.95 21.3149 24.8289 21.3999 24.5627L24.9329 13.4993C24.9004 13.5084 24.8676 13.5172 24.8344 13.5259C23.3017 13.9271 20.9878 13.945 17.5008 13.2038C14.0167 12.4632 11.909 11.5173 10.6708 10.5333C10.6432 10.5114 10.616 10.4894 10.5893 10.4674L9.72274 18.3297C9.91734 18.2983 10.1178 18.2596 10.3234 18.2135C11.6478 17.9163 13.0978 17.3274 14.3989 16.49C14.3535 16.241 14.355 15.9792 14.4108 15.7162C14.6405 14.6358 15.7025 13.9461 16.783 14.1758C17.8634 14.4054 18.5531 15.4675 18.3234 16.5479C18.0938 17.6283 17.0318 18.318 15.9513 18.0884C15.7872 18.0535 15.6321 17.9994 15.4879 17.9289C13.9672 18.9287 12.272 19.621 10.7175 19.9698ZM6.81448 17.9635C6.96271 18.1317 7.29322 18.318 7.90449 18.3963L8.4582 13.3724C7.86855 14.1357 7.39981 14.8757 7.07875 15.5516C6.4258 16.9264 6.54311 17.6557 6.81448 17.9635Z"
        fill="#FAFAFA"
      />
    </svg>
  );
};

export const EmbucketLogoText = () => (
  <svg width="94" height="18" viewBox="0 0 94 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M6.04865 17.2532C4.81262 17.2532 3.74811 16.9891 2.85511 16.4609C1.96757 15.9273 1.28149 15.1786 0.796875 14.2148C0.317708 13.2456 0.078125 12.1103 0.078125 10.8089C0.078125 9.52391 0.317708 8.39133 0.796875 7.41122C1.28149 6.43111 1.95668 5.66607 2.82244 5.11612C3.69366 4.56617 4.71188 4.29119 5.87713 4.29119C6.58499 4.29119 7.27107 4.40826 7.93537 4.6424C8.59967 4.87654 9.1959 5.24408 9.72408 5.74503C10.2522 6.24598 10.6688 6.89666 10.9737 7.69709C11.2786 8.49207 11.4311 9.45857 11.4311 10.5966V11.4624H1.45845V9.63281H9.038C9.038 8.99029 8.90732 8.42128 8.64595 7.92578C8.38459 7.42483 8.01705 7.03007 7.54332 6.74148C7.07505 6.45289 6.52509 6.30859 5.89347 6.30859C5.20739 6.30859 4.60843 6.47739 4.09659 6.81499C3.5902 7.14714 3.19815 7.58274 2.92045 8.1218C2.6482 8.65542 2.51207 9.23532 2.51207 9.86151V11.2908C2.51207 12.1294 2.65909 12.8427 2.95312 13.4308C3.2526 14.0188 3.66915 14.468 4.20277 14.7784C4.73639 15.0833 5.35985 15.2358 6.07315 15.2358C6.53599 15.2358 6.95798 15.1705 7.33913 15.0398C7.72029 14.9036 8.04972 14.7022 8.32741 14.4354C8.60511 14.1686 8.81747 13.8391 8.96449 13.4471L11.2759 13.8636C11.0908 14.5443 10.7586 15.1405 10.2795 15.6523C9.80575 16.1587 9.20952 16.5535 8.49077 16.8366C7.77746 17.1143 6.96342 17.2532 6.04865 17.2532ZM12.1407 17V4.45455H14.4848V6.49645H14.64C14.9014 5.80492 15.3288 5.26586 15.9223 4.87926C16.5158 4.48722 17.2264 4.29119 18.0541 4.29119C18.8926 4.29119 19.595 4.48722 20.1613 4.87926C20.733 5.27131 21.155 5.81037 21.4273 6.49645H21.558C21.8575 5.8267 22.3339 5.29309 22.9873 4.8956C23.6407 4.49266 24.4194 4.29119 25.3232 4.29119C26.4613 4.29119 27.3896 4.64785 28.1084 5.36115C28.8326 6.07446 29.1947 7.14986 29.1947 8.58736V17H26.7526V8.81605C26.7526 7.96662 26.5212 7.35133 26.0583 6.97017C25.5955 6.58902 25.0428 6.39844 24.4003 6.39844C23.6053 6.39844 22.9873 6.64347 22.5463 7.13352C22.1052 7.61813 21.8847 8.2416 21.8847 9.00391V17H19.4507V8.66087C19.4507 7.98023 19.2384 7.433 18.8137 7.01918C18.3889 6.60535 17.8363 6.39844 17.1556 6.39844C16.6928 6.39844 16.2654 6.52095 15.8733 6.76598C15.4867 7.00556 15.1736 7.34044 14.934 7.7706C14.6999 8.20076 14.5828 8.69898 14.5828 9.26527V17H12.1407ZM30.6639 17V0.272727H33.106V6.48828H33.253C33.3946 6.22692 33.5988 5.92472 33.8656 5.58168C34.1324 5.23864 34.5027 4.93916 34.9764 4.68324C35.4501 4.42188 36.0763 4.29119 36.8549 4.29119C37.8677 4.29119 38.7716 4.54711 39.5666 5.05895C40.3616 5.57079 40.985 6.30859 41.437 7.27237C41.8944 8.23615 42.123 9.39595 42.123 10.7518C42.123 12.1076 41.8971 13.2701 41.4451 14.2393C40.9932 15.2031 40.3725 15.9464 39.5829 16.4691C38.7934 16.9864 37.8922 17.245 36.8794 17.245C36.1171 17.245 35.4937 17.1171 35.0091 16.8612C34.5299 16.6052 34.1542 16.3058 33.8819 15.9627C33.6097 15.6197 33.4 15.3147 33.253 15.0479H33.0488V17H30.6639ZM33.057 10.7273C33.057 11.6094 33.185 12.3826 33.4409 13.0469C33.6968 13.7112 34.0671 14.2312 34.5517 14.6069C35.0363 14.9772 35.6298 15.1623 36.3322 15.1623C37.0618 15.1623 37.6717 14.969 38.1618 14.5824C38.6518 14.1903 39.0221 13.6594 39.2725 12.9897C39.5285 12.32 39.6564 11.5658 39.6564 10.7273C39.6564 9.89962 39.5312 9.15637 39.2807 8.49751C39.0357 7.83866 38.6654 7.31866 38.1699 6.9375C37.6799 6.55634 37.0673 6.36577 36.3322 6.36577C35.6243 6.36577 35.0254 6.54818 34.5353 6.913C34.0507 7.27782 33.6832 7.78693 33.4327 8.44034C33.1822 9.09375 33.057 9.85606 33.057 10.7273ZM50.8124 11.7972V4.45455H53.2627V17H50.8614V14.8274H50.7307C50.4421 15.4972 49.9793 16.0553 49.3422 16.5018C48.7106 16.9428 47.9238 17.1634 46.9818 17.1634C46.1759 17.1634 45.4626 16.9864 44.8419 16.6325C44.2266 16.2731 43.742 15.7422 43.3881 15.0398C43.0396 14.3374 42.8653 13.4689 42.8653 12.4343V4.45455H45.3074V12.1403C45.3074 12.9951 45.5443 13.6758 46.018 14.1822C46.4917 14.6886 47.107 14.9418 47.8639 14.9418C48.3213 14.9418 48.776 14.8274 49.2279 14.5987C49.6853 14.37 50.0637 14.0243 50.3632 13.5614C50.6681 13.0986 50.8179 12.5105 50.8124 11.7972ZM59.8285 17.2532C58.6142 17.2532 57.5688 16.9782 56.6921 16.4283C55.8209 15.8729 55.1512 15.1078 54.6829 14.1332C54.2146 13.1585 53.9805 12.0423 53.9805 10.7844C53.9805 9.5103 54.2201 8.38589 54.6992 7.41122C55.1784 6.43111 55.8536 5.66607 56.7248 5.11612C57.596 4.56617 58.6224 4.29119 59.804 4.29119C60.7569 4.29119 61.6063 4.46816 62.3523 4.82209C63.0982 5.17057 63.6999 5.66063 64.1573 6.29226C64.6201 6.92389 64.8951 7.66169 64.9822 8.50568H62.6055C62.4748 7.91761 62.1753 7.41122 61.707 6.98651C61.2442 6.56179 60.6235 6.34943 59.8448 6.34943C59.1642 6.34943 58.5679 6.52912 58.0561 6.88849C57.5497 7.24242 57.1549 7.74882 56.8718 8.40767C56.5887 9.06108 56.4471 9.83428 56.4471 10.7273C56.4471 11.642 56.5859 12.4316 56.8636 13.0959C57.1413 13.7602 57.5334 14.2747 58.0398 14.6396C58.5516 15.0044 59.1533 15.1868 59.8448 15.1868C60.3076 15.1868 60.7269 15.1024 61.1026 14.9336C61.4838 14.7594 61.8023 14.5116 62.0582 14.1903C62.3196 13.8691 62.502 13.4825 62.6055 13.0305H64.9822C64.8951 13.8419 64.631 14.5661 64.19 15.2031C63.7489 15.8402 63.1581 16.3411 62.4176 16.706C61.6825 17.0708 60.8195 17.2532 59.8285 17.2532ZM67.8399 12.7447L67.8236 9.76349H68.2483L73.2469 4.45455H76.1709L70.4699 10.4986H70.086L67.8399 12.7447ZM65.5938 17V0.272727H68.036V17H65.5938ZM73.5164 17L69.0242 11.0376L70.7068 9.33061L76.5139 17H73.5164ZM81.2869 17.2532C80.0509 17.2532 78.9864 16.9891 78.0934 16.4609C77.2058 15.9273 76.5198 15.1786 76.0352 14.2148C75.556 13.2456 75.3164 12.1103 75.3164 10.8089C75.3164 9.52391 75.556 8.39133 76.0352 7.41122C76.5198 6.43111 77.195 5.66607 78.0607 5.11612C78.9319 4.56617 79.9502 4.29119 81.1154 4.29119C81.8233 4.29119 82.5094 4.40826 83.1737 4.6424C83.838 4.87654 84.4342 5.24408 84.9624 5.74503C85.4905 6.24598 85.9071 6.89666 86.212 7.69709C86.5169 8.49207 86.6694 9.45857 86.6694 10.5966V11.4624H76.6967V9.63281H84.2763C84.2763 8.99029 84.1456 8.42128 83.8842 7.92578C83.6229 7.42483 83.2553 7.03007 82.7816 6.74148C82.3133 6.45289 81.7634 6.30859 81.1317 6.30859C80.4457 6.30859 79.8467 6.47739 79.3349 6.81499C78.8285 7.14714 78.4364 7.58274 78.1587 8.1218C77.8865 8.65542 77.7504 9.23532 77.7504 9.86151V11.2908C77.7504 12.1294 77.8974 12.8427 78.1914 13.4308C78.4909 14.0188 78.9074 14.468 79.4411 14.7784C79.9747 15.0833 80.5981 15.2358 81.3114 15.2358C81.7743 15.2358 82.1963 15.1705 82.5774 15.0398C82.9586 14.9036 83.288 14.7022 83.5657 14.4354C83.8434 14.1686 84.0558 13.8391 84.2028 13.4471L86.5142 13.8636C86.3291 14.5443 85.9969 15.1405 85.5178 15.6523C85.044 16.1587 84.4478 16.5535 83.729 16.8366C83.0157 17.1143 82.2017 17.2532 81.2869 17.2532ZM93.2352 4.45455V6.41477H86.3825V4.45455H93.2352ZM88.2203 1.44886H90.6624V13.3164C90.6624 13.7901 90.7332 14.1468 90.8747 14.3864C91.0163 14.6205 91.1987 14.7811 91.422 14.8683C91.6507 14.9499 91.8984 14.9908 92.1652 14.9908C92.3612 14.9908 92.5328 14.9772 92.6798 14.9499C92.8268 14.9227 92.9411 14.9009 93.0228 14.8846L93.4639 16.902C93.3223 16.9564 93.1208 17.0109 92.8595 17.0653C92.5981 17.1252 92.2714 17.1579 91.8794 17.1634C91.2368 17.1742 90.6379 17.0599 90.0825 16.8203C89.5271 16.5807 89.0779 16.2105 88.7348 15.7095C88.3918 15.2086 88.2203 14.5797 88.2203 13.8228V1.44886Z"
      fill="#FAFAFA"
    />
  </svg>
);
