@import './layout/layout.css' layer(base);

@import 'tailwindcss';

@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --font-*: initial;
  --font-sans: Inter, sans-serif;

  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-brand-1: var(--brand-1);
  --color-brand-2: var(--brand-2);

  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);

  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);

  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-secondary-accent: var(--sidebar-secondary-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
  @media (width >= --theme(--breakpoint-sm)) {
    max-width: none;
  }
  @media (width >= 1400px) {
    max-width: 1400px;
  }
}

:root {
  /* Brand colors (used for component from/to gradients)*/
  --brand-1: hsl(70 66% 87%);
  --brand-2: hsl(230 75% 78%);
  /* Default background color of <body />...etc */
  --background: hsl(0 0% 100%);
  --foreground: hsl(222.2 84% 4.9%);
  /* Background color for <Card /> */
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(222.2 84% 4.9%);
  /* Background color for popovers such as <DropdownMenu />, <HoverCard />, <Popover /> */
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(222.2 84% 4.9%);
  /* Primary colors for <Button /> */
  --primary: hsl(222.2 47.4% 11.2%);
  --primary-foreground: hsl(210 40% 98%);
  /* Secondary colors for <Button /> */
  --secondary: hsl(210 40% 96.1%);
  --secondary-foreground: hsl(222.2 47.4% 11.2%);
  /* Muted backgrounds such as <TabsList />, <Skeleton /> and <Switch /> */
  --muted: hsl(210 40% 96.1%);
  --muted-foreground: hsl(215.4 16.3% 46.9%);
  /* Used for accents such as hover effects on <DropdownMenuItem>, <SelectItem>...etc */
  --accent: hsl(210 40% 96.1%);
  --accent-foreground: hsl(222.2 47.4% 11.2%);
  /* Used for destructive actions such as <Button variant="destructive"> */
  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(210 40% 98%);
  /* Default border color */
  --border: hsl(214.3 31.8% 91.4%);
  /* Border color for inputs such as <Input />, <Select />, <Textarea /> */
  --input: hsl(214.3 31.8% 91.4%);
  /* Used for focus ring */
  --ring: hsl(222.2 84% 4.9%);
  /* Border radius for card, input and buttons */
  --radius: 0.5rem;
  /* Chart colors */
  --chart-1: hsl(12 76% 61%);
  --chart-2: hsl(173 58% 39%);
  --chart-3: hsl(197 37% 24%);
  --chart-4: hsl(43 74% 66%);
  --chart-5: hsl(27 87% 67%);
  --sidebar: hsl(0 0% 98%);
  --sidebar-foreground: hsl(240 5.3% 26.1%);
  --sidebar-primary: hsl(240 5.9% 10%);
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(240, 5%, 96%);
  --sidebar-accent-foreground: hsl(240 5.9% 10%);
  --sidebar-border: hsl(220 13% 91%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

.dark {
  /* color1: BGs, Primary foreground */
  /* color2: Primary, foregrounds */
  /* color3: Muted, accent */
  /* color4: Borders */
  /* color5: Secondary button bg */
  /* 
    --color1: 225 38% 8%;
    --color2: 0 0% 98%;
    --color3: 224 19% 15%;
    --color4: 240 2% 19%;
    --color5: var(--color3); */
  /* 
    --color1: 0 0% 2%;
    --color2: 0 0% 98%;
    --color3: 240 2% 9%;
    --color4: 240 2% 19%;
    --color5: var(--color3); */

  --color1: hsl(240 5% 9%);
  --color2: hsl(0 0% 98%);
  --color3: hsl(228 8% 12%);
  --color4: hsl(240 2% 19%);
  --color5: hsl(0 0% 14.9%);

  /* Default background color of <body />...etc */
  --background: #171717;
  --foreground: var(--color2);
  /* Background color for <Card /> */
  --card: var(--color1);
  --card-foreground: var(--color2);
  /* Background color for popovers such as <DropdownMenu />, <HoverCard />, <Popover /> */
  --popover: var(--color1);
  --popover-foreground: var(--color2);
  /* Primary colors for <Button /> */
  --primary: #0064d2;
  --primary-foreground: #ffffff;
  /* Secondary colors for <Button /> */
  --secondary: var(--color5);
  --secondary-foreground: var(--color2);
  /* Muted backgrounds such as <TabsList />, <Skeleton /> and <Switch /> */
  --muted: #272727;
  --muted-foreground: #888888;
  /* Used for accents such as hover effects on   <DropdownMenuItem>, <SelectItem>...etc */
  --accent: #2e2e2e;
  --accent-foreground: var(--color2);
  /* Used for destructive actions such as <Button variant="destructive"> */
  --destructive: hsl(0 100% 50%);
  --destructive-foreground: var(--color2);
  /* Default border color */
  --border: var(--color4);
  /* Border color for inputs such as <Input />, <Select />, <Textarea /> */
  --input: var(--color4);
  /* Used for focus ring */
  --ring: hsl(0 0% 83.1%);
  /* Chart colors */
  --chart-1: hsl(220 70% 50%);
  --chart-2: hsl(160 60% 45%);
  --chart-3: hsl(30 80% 55%);
  --chart-4: hsl(280 65% 60%);
  --chart-5: hsl(340 75% 55%);

  --sidebar: #171717;
  /* Sidebar - Bg hover + Active bg */
  --sidebar-accent: #0064d2;
  /* Active + Hover Text */
  --sidebar-accent-foreground: #ffffff;
  /* Default - White */
  --sidebar-foreground: #ffffff;
  --sidebar-border: #2e2e2e;
  --sidebar-ring: hsl(217.2 91.2% 59.8%);

  /* Icons - Muted foreground */

  /* Secondary bg hover + Active bg */
  --sidebar-secondary-accent: #333333;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    overflow: hidden;
  }
}

/* ::-webkit-scrollbar {
  display: none;
} */

.recharts-wrapper {
  margin: 0 auto;
}
