/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root';
import { Route as VolumesImport } from './routes/volumes';
import { Route as HomeImport } from './routes/home';
import { Route as IndexImport } from './routes/index';
import { Route as QueriesIndexImport } from './routes/queries/index';
import { Route as DatabasesIndexImport } from './routes/databases/index';
import { Route as SqlEditorWorksheetIdIndexImport } from './routes/sql-editor/$worksheetId.index';
import { Route as QueriesQueryIdIndexImport } from './routes/queries/$queryId.index';
import { Route as DatabasesDatabaseNameSchemasIndexImport } from './routes/databases/$databaseName.schemas.index';
import { Route as DatabasesDatabaseNameSchemasSchemaNameTablesIndexImport } from './routes/databases/$databaseName.schemas.$schemaName.tables.index';
import { Route as DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexImport } from './routes/databases/$databaseName.schemas.$schemaName.tables.$tableName.columns.index';

// Create/Update Routes

const VolumesRoute = VolumesImport.update({
  id: '/volumes',
  path: '/volumes',
  getParentRoute: () => rootRoute,
} as any);

const HomeRoute = HomeImport.update({
  id: '/home',
  path: '/home',
  getParentRoute: () => rootRoute,
} as any);

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any);

const QueriesIndexRoute = QueriesIndexImport.update({
  id: '/queries/',
  path: '/queries/',
  getParentRoute: () => rootRoute,
} as any);

const DatabasesIndexRoute = DatabasesIndexImport.update({
  id: '/databases/',
  path: '/databases/',
  getParentRoute: () => rootRoute,
} as any);

const SqlEditorWorksheetIdIndexRoute = SqlEditorWorksheetIdIndexImport.update({
  id: '/sql-editor/$worksheetId/',
  path: '/sql-editor/$worksheetId/',
  getParentRoute: () => rootRoute,
} as any);

const QueriesQueryIdIndexRoute = QueriesQueryIdIndexImport.update({
  id: '/queries/$queryId/',
  path: '/queries/$queryId/',
  getParentRoute: () => rootRoute,
} as any);

const DatabasesDatabaseNameSchemasIndexRoute =
  DatabasesDatabaseNameSchemasIndexImport.update({
    id: '/databases/$databaseName/schemas/',
    path: '/databases/$databaseName/schemas/',
    getParentRoute: () => rootRoute,
  } as any);

const DatabasesDatabaseNameSchemasSchemaNameTablesIndexRoute =
  DatabasesDatabaseNameSchemasSchemaNameTablesIndexImport.update({
    id: '/databases/$databaseName/schemas/$schemaName/tables/',
    path: '/databases/$databaseName/schemas/$schemaName/tables/',
    getParentRoute: () => rootRoute,
  } as any);

const DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRoute =
  DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexImport.update(
    {
      id: '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns/',
      path: '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns/',
      getParentRoute: () => rootRoute,
    } as any,
  );

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/';
      path: '/';
      fullPath: '/';
      preLoaderRoute: typeof IndexImport;
      parentRoute: typeof rootRoute;
    };
    '/home': {
      id: '/home';
      path: '/home';
      fullPath: '/home';
      preLoaderRoute: typeof HomeImport;
      parentRoute: typeof rootRoute;
    };
    '/volumes': {
      id: '/volumes';
      path: '/volumes';
      fullPath: '/volumes';
      preLoaderRoute: typeof VolumesImport;
      parentRoute: typeof rootRoute;
    };
    '/databases/': {
      id: '/databases/';
      path: '/databases';
      fullPath: '/databases';
      preLoaderRoute: typeof DatabasesIndexImport;
      parentRoute: typeof rootRoute;
    };
    '/queries/': {
      id: '/queries/';
      path: '/queries';
      fullPath: '/queries';
      preLoaderRoute: typeof QueriesIndexImport;
      parentRoute: typeof rootRoute;
    };
    '/queries/$queryId/': {
      id: '/queries/$queryId/';
      path: '/queries/$queryId';
      fullPath: '/queries/$queryId';
      preLoaderRoute: typeof QueriesQueryIdIndexImport;
      parentRoute: typeof rootRoute;
    };
    '/sql-editor/$worksheetId/': {
      id: '/sql-editor/$worksheetId/';
      path: '/sql-editor/$worksheetId';
      fullPath: '/sql-editor/$worksheetId';
      preLoaderRoute: typeof SqlEditorWorksheetIdIndexImport;
      parentRoute: typeof rootRoute;
    };
    '/databases/$databaseName/schemas/': {
      id: '/databases/$databaseName/schemas/';
      path: '/databases/$databaseName/schemas';
      fullPath: '/databases/$databaseName/schemas';
      preLoaderRoute: typeof DatabasesDatabaseNameSchemasIndexImport;
      parentRoute: typeof rootRoute;
    };
    '/databases/$databaseName/schemas/$schemaName/tables/': {
      id: '/databases/$databaseName/schemas/$schemaName/tables/';
      path: '/databases/$databaseName/schemas/$schemaName/tables';
      fullPath: '/databases/$databaseName/schemas/$schemaName/tables';
      preLoaderRoute: typeof DatabasesDatabaseNameSchemasSchemaNameTablesIndexImport;
      parentRoute: typeof rootRoute;
    };
    '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns/': {
      id: '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns/';
      path: '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns';
      fullPath: '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns';
      preLoaderRoute: typeof DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexImport;
      parentRoute: typeof rootRoute;
    };
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute;
  '/home': typeof HomeRoute;
  '/volumes': typeof VolumesRoute;
  '/databases': typeof DatabasesIndexRoute;
  '/queries': typeof QueriesIndexRoute;
  '/queries/$queryId': typeof QueriesQueryIdIndexRoute;
  '/sql-editor/$worksheetId': typeof SqlEditorWorksheetIdIndexRoute;
  '/databases/$databaseName/schemas': typeof DatabasesDatabaseNameSchemasIndexRoute;
  '/databases/$databaseName/schemas/$schemaName/tables': typeof DatabasesDatabaseNameSchemasSchemaNameTablesIndexRoute;
  '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns': typeof DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRoute;
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute;
  '/home': typeof HomeRoute;
  '/volumes': typeof VolumesRoute;
  '/databases': typeof DatabasesIndexRoute;
  '/queries': typeof QueriesIndexRoute;
  '/queries/$queryId': typeof QueriesQueryIdIndexRoute;
  '/sql-editor/$worksheetId': typeof SqlEditorWorksheetIdIndexRoute;
  '/databases/$databaseName/schemas': typeof DatabasesDatabaseNameSchemasIndexRoute;
  '/databases/$databaseName/schemas/$schemaName/tables': typeof DatabasesDatabaseNameSchemasSchemaNameTablesIndexRoute;
  '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns': typeof DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRoute;
}

export interface FileRoutesById {
  __root__: typeof rootRoute;
  '/': typeof IndexRoute;
  '/home': typeof HomeRoute;
  '/volumes': typeof VolumesRoute;
  '/databases/': typeof DatabasesIndexRoute;
  '/queries/': typeof QueriesIndexRoute;
  '/queries/$queryId/': typeof QueriesQueryIdIndexRoute;
  '/sql-editor/$worksheetId/': typeof SqlEditorWorksheetIdIndexRoute;
  '/databases/$databaseName/schemas/': typeof DatabasesDatabaseNameSchemasIndexRoute;
  '/databases/$databaseName/schemas/$schemaName/tables/': typeof DatabasesDatabaseNameSchemasSchemaNameTablesIndexRoute;
  '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns/': typeof DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRoute;
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath;
  fullPaths:
    | '/'
    | '/home'
    | '/volumes'
    | '/databases'
    | '/queries'
    | '/queries/$queryId'
    | '/sql-editor/$worksheetId'
    | '/databases/$databaseName/schemas'
    | '/databases/$databaseName/schemas/$schemaName/tables'
    | '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns';
  fileRoutesByTo: FileRoutesByTo;
  to:
    | '/'
    | '/home'
    | '/volumes'
    | '/databases'
    | '/queries'
    | '/queries/$queryId'
    | '/sql-editor/$worksheetId'
    | '/databases/$databaseName/schemas'
    | '/databases/$databaseName/schemas/$schemaName/tables'
    | '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns';
  id:
    | '__root__'
    | '/'
    | '/home'
    | '/volumes'
    | '/databases/'
    | '/queries/'
    | '/queries/$queryId/'
    | '/sql-editor/$worksheetId/'
    | '/databases/$databaseName/schemas/'
    | '/databases/$databaseName/schemas/$schemaName/tables/'
    | '/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns/';
  fileRoutesById: FileRoutesById;
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute;
  HomeRoute: typeof HomeRoute;
  VolumesRoute: typeof VolumesRoute;
  DatabasesIndexRoute: typeof DatabasesIndexRoute;
  QueriesIndexRoute: typeof QueriesIndexRoute;
  QueriesQueryIdIndexRoute: typeof QueriesQueryIdIndexRoute;
  SqlEditorWorksheetIdIndexRoute: typeof SqlEditorWorksheetIdIndexRoute;
  DatabasesDatabaseNameSchemasIndexRoute: typeof DatabasesDatabaseNameSchemasIndexRoute;
  DatabasesDatabaseNameSchemasSchemaNameTablesIndexRoute: typeof DatabasesDatabaseNameSchemasSchemaNameTablesIndexRoute;
  DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRoute: typeof DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRoute;
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  HomeRoute: HomeRoute,
  VolumesRoute: VolumesRoute,
  DatabasesIndexRoute: DatabasesIndexRoute,
  QueriesIndexRoute: QueriesIndexRoute,
  QueriesQueryIdIndexRoute: QueriesQueryIdIndexRoute,
  SqlEditorWorksheetIdIndexRoute: SqlEditorWorksheetIdIndexRoute,
  DatabasesDatabaseNameSchemasIndexRoute:
    DatabasesDatabaseNameSchemasIndexRoute,
  DatabasesDatabaseNameSchemasSchemaNameTablesIndexRoute:
    DatabasesDatabaseNameSchemasSchemaNameTablesIndexRoute,
  DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRoute:
    DatabasesDatabaseNameSchemasSchemaNameTablesTableNameColumnsIndexRoute,
};

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>();

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/home",
        "/volumes",
        "/databases/",
        "/queries/",
        "/queries/$queryId/",
        "/sql-editor/$worksheetId/",
        "/databases/$databaseName/schemas/",
        "/databases/$databaseName/schemas/$schemaName/tables/",
        "/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns/"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/home": {
      "filePath": "home.tsx"
    },
    "/volumes": {
      "filePath": "volumes.tsx"
    },
    "/databases/": {
      "filePath": "databases/index.tsx"
    },
    "/queries/": {
      "filePath": "queries/index.tsx"
    },
    "/queries/$queryId/": {
      "filePath": "queries/$queryId.index.tsx"
    },
    "/sql-editor/$worksheetId/": {
      "filePath": "sql-editor/$worksheetId.index.tsx"
    },
    "/databases/$databaseName/schemas/": {
      "filePath": "databases/$databaseName.schemas.index.tsx"
    },
    "/databases/$databaseName/schemas/$schemaName/tables/": {
      "filePath": "databases/$databaseName.schemas.$schemaName.tables.index.tsx"
    },
    "/databases/$databaseName/schemas/$schemaName/tables/$tableName/columns/": {
      "filePath": "databases/$databaseName.schemas.$schemaName.tables.$tableName.columns.index.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
