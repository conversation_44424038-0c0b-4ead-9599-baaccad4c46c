{"name": "@embucket/web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"build": "tsc -b && vite build", "codegen": "orval --config ./orval.config.ts", "dev": "vite", "format": "prettier --write .", "preinstall": "npx only-allow pnpm", "postinstall": "npx simple-git-hooks", "knip": "knip", "lint": "eslint . --fix --report-unused-disable-directives --max-warnings 0", "ncu": "ncu -u", "preview": "vite preview", "seed": "node ./seed.ts", "typecheck": "tsc -p ./tsconfig.app.json"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "dependencies": {"@codemirror/autocomplete": "^6.18.6", "@codemirror/commands": "^6.8.1", "@codemirror/lang-sql": "^6.9.0", "@codemirror/language": "^6.11.1", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.37.1", "@hookform/resolvers": "^5.1.1", "@lezer/highlight": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query": "^5.80.6", "@tanstack/react-query-devtools": "^5.80.6", "@tanstack/react-router": "1.120.20", "@tanstack/react-table": "^8.21.3", "@tidbcloud/codemirror-extension-cur-sql-gutter": "^0.0.6", "@tidbcloud/codemirror-extension-events": "^0.0.7", "@tidbcloud/codemirror-extension-linters": "^0.0.6", "@tidbcloud/codemirror-extension-save-helper": "^0.0.6", "@tidbcloud/codemirror-extension-sql-parser": "^0.0.6", "@tidbcloud/tisqleditor-react": "^0.0.6", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "i18next": "^25.2.1", "lucide-react": "^0.513.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.57.0", "react-i18next": "^15.5.2", "react-resizable-panels": "^3.0.2", "recharts": "^2.15.3", "sonner": "^2.0.5", "sql-formatter": "^15.6.4", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.3.4", "use-local-storage": "^3.0.0", "zod": "^3.25.57", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "9.28.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.2", "@julr/vite-plugin-validate-env": "^2.1.0", "@orval/core": "^7.9.0", "@tanstack/eslint-plugin-query": "^5.78.0", "@tanstack/router-devtools": "1.120.20", "@tanstack/router-plugin": "1.120.20", "@types/node": "22.15.30", "@types/react": "^19.1.7", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "9.28.0", "eslint-plugin-check-file": "^3.3.0", "eslint-plugin-n": "^17.19.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "knip": "^5.60.2", "lint-staged": "^16.1.0", "npm-check-updates": "^18.0.1", "orval": "^7.9.0", "prettier": "^3.5.3", "prettier-plugin-packagejson": "^2.5.15", "prettier-plugin-tailwindcss": "^0.6.12", "simple-git-hooks": "^2.13.0", "tailwindcss": "^4.1.8", "typescript": "^5.8.3", "typescript-eslint": "^8.34.0", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4"}, "packageManager": "pnpm@10.12.1", "engines": {"node": ">=v22.16.0", "npm": "Please, use pnpm", "yarn": "Please, use pnpm"}}